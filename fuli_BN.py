# --- START OF FILE ARGM_V6_Singularity.py ---

# ------------------------------------------------------------------------------------
# ARGM-V6.0 "Singularity" - 自适应趋势网格复投策略
# ------------------------------------------------------------------------------------
# 作者: Lens & AI
# 日期: 2025-07-11
# 描述: 本策略是基于第一性原理的自适应网格系统。
#       它将网格的位置、范围、密度及重绘决策统一到一个核心函数中，
#       在捕捉均值回归利润的同时，集成了趋势过滤、智能风险控制和利润复投机制。
# 版本历史:
# V6.0: 整合入专业的异步交易框架，替换原有的做市逻辑。
#       - 引入单一决策源函数 generate_grid_state()。
#       - 引入趋势方向手动覆盖开关。
#       - 集成智能风险控制与盈利复投模块。
#       - 优化重绘逻辑为重叠度检测，大幅降低交易成本。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import math
import os
import sys
import uuid
import pandas as pd
import numpy as np
from collections import namedtuple
import aiohttp
import ccxt.async_support as ccxt_async


# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "USlBuHx9zNwxXFtecEQZDOXXxbHhKMIViMeRaELIyjOHGCcdkoESgmxpu4bWi47c",      # ❗❗❗ 替换为你的 API Key
    "api_secret": "lxNiaI54wlWAyxpZ5oNajJ84MoQYodnakDWfoQiuONNTtU9YKsKS14tRwgBi7eUt",  # ❗❗❗ 替换为你的 API Secret
    "use_proxy": True,
    "proxy_url": "http://127.0.0.1:7897",

    # --- 策略核心参数 ---
    "coin_name": "SOL",
    "contract_type": "USDT",
    "leverage": 20,
    "initial_value": 20, # [重构] 含义变为：每格订单的基础名义价值

    # --- [新增] ARGM-V6.0 核心逻辑配置 ---
    "argm": {
        "ema_trend_period": 200,      # 宏观趋势判定周期
        "ema_anchor_period": 96,       # 网格中枢平滑周期
        "atr_period": 14,              # 波动率计算周期
        "rsi_period": 14,              # 动能/情绪计算周期
        "grid_total_levels": 30,       # 网格总层数 (固定值，密度由区间宽度自适应)
        "repaint_overlap_ratio": 0.6,  # 新旧网格重合度低于此值则重绘
        "kline_timeframe": '5m',       # 用于计算指标的K线周期
        # [新增] 趋势方向手动覆盖开关: "AUTO", "LONG_ONLY", "SHORT_ONLY"
        "trend_override": "AUTO",
    },

    # --- [新增] 智能风险与复投配置 ---
    "risk_management": {
        # ✅ 多/空头寸合约数量的硬性上限，这是最重要的风控参数
        "position_limit_contracts": 0.5,
        # 智能风险恢复模块参数
        "smart_recovery_config": {'add_multiplier': 1.3, 'reduce_multiplier': 0.7, 'pause_threshold': 3, 'pause_bars': 4},
        # 盈利复投模块参数
        "reinvest_config": {'threshold_usd': 500, 'profit_ratio_multiplier': 0.2, 'size_cap_multiplier': 2.0}
    },

    # --- 系统运行参数 (基本保持不变) ---
    "system": {
        "sync_interval_seconds": 60,
        "timeout_seconds": 30,
        "ticker_update_interval_ms": 500, # 适当放宽，避免过于频繁调整
        "max_connection_retries": 5,
        "connection_retry_delay": 5,
        "state_sync_on_error": True,
        "websocket_ping_interval": 20,
        "websocket_timeout": 60
    }
}


# ====================================================================================
# --- 日志配置 (保持不变) ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class ARGMStrategyBot:
    # [新增] 定义GridState数据结构
    GridState = namedtuple('GridState', ['grids', 'center', 'low', 'high', 'should_repaint'])

    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()

        self.exchange = self._initialize_exchange()
        self.symbol = f"{config['coin_name']}/{config['contract_type']}:{config['contract_type']}"
        self.market = None
        
        contract_type = self.config['contract_type'].upper()
        if contract_type in ['USDT', 'USDC']:
            self.websocket_base_url = "wss://fstream.binance.com"
        else:
            logger.error(f"不支持的合约类型: {contract_type}"); sys.exit(1)
        logger.info(f"已设置WebSocket URL: {self.websocket_base_url}")

        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0
        self.price_series = pd.DataFrame(columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # --- 核心策略状态 ---
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {}
        self.current_grids = [] # [新增] 当前生效的网格

        # [新增] 智能风控与复投状态
        self.consecutive_losses = 0
        self.total_profit_usd = 0.0
        self.is_paused = False
        self.pause_end_time = 0

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_ticker_time = 0
        self.is_shutting_down = False

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if "YOUR_API_KEY" in self.config['api_key'] or "YOUR_API_SECRET" in self.config['api_secret']:
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        exchange_class = ccxt_async.binanceusdm

        class CustomGate(exchange_class):
            async def fetch(self, url, method='GET', headers=None, body=None):
                if headers is None: headers = {}
                headers['User-Agent'] = 'Mozilla/5.0'
                return await super().fetch(url, method, headers, body)

        proxies = {'http': self.config['proxy_url'], 'https': self.config['proxy_url']} if self.config['use_proxy'] else None
        if proxies: logger.info(f"已设置代理: {self.config['proxy_url']}")

        options = {
            'defaultType': 'future',
            'adjustForTimeDifference': True,
            'recvWindow': 10000,  # 增加接收窗口时间
            'warnOnFetchOpenOrdersWithoutSymbol': False,
            'createMarketBuyOrderRequiresPrice': False
        }

        exchange = CustomGate({
            'apiKey': self.config['api_key'],
            'secret': self.config['api_secret'],
            'timeout': 60000,  # 增加超时时间到60秒
            'enableRateLimit': True,
            'options': options,
            'proxies': proxies
        })
        
        logger.info("交易所实例创建成功")
        return exchange
    
    async def test_connection(self):
        """测试网络连接和API可用性"""
        max_retries = 3
        retry_delay = 5
        
        for attempt in range(max_retries):
            try:
                logger.info(f"正在测试API连接... (尝试 {attempt + 1}/{max_retries})")
                    
                # [修改] 使用fapi的ping端点替代sapi的状态检查，更符合期货交易场景
                logger.info("正在检查Futures API连通性 (ping)...")
                try:
                    ping_result = await asyncio.wait_for(
                        self.exchange.fapiPublicGetPing(), timeout=30
                    )
                    logger.info(f"Ping 成功: {ping_result}")
                except Exception as ping_error:
                    logger.error(f"Ping失败: {ping_error}")
                    logger.error(f"错误类型: {type(ping_error).__name__}")
                    # 尝试使用更基础的方法
                    logger.info("尝试使用基础HTTP请求测试连接...")
                    proxy = self.config['proxy_url'] if self.config.get('use_proxy') else None
                    timeout = aiohttp.ClientTimeout(total=30)
                    try:
                        async with aiohttp.ClientSession(timeout=timeout) as session:
                            async with session.get("https://fapi.binance.com/fapi/v1/ping", proxy=proxy) as response:
                                if response.status == 200:
                                    logger.info("基础HTTP ping成功")
                                else:
                                    logger.warning(f"HTTP ping状态码: {response.status}")
                    except Exception as http_error:
                        logger.error(f"基础HTTP ping也失败: {http_error}")
                        logger.error(f"HTTP错误类型: {type(http_error).__name__}")
                
                # 测试获取ticker数据
                logger.info("正在测试ticker数据获取...")
                try:
                    ticker = await asyncio.wait_for(
                        self.exchange.fetch_ticker(self.symbol), timeout=30
                    )
                    logger.info(f"Ticker 获取成功: {ticker['symbol']} @ {ticker['last']}")
                except Exception as ticker_error:
                    logger.error(f"Ticker获取失败: {ticker_error}")
                    # 如果ticker失败，可能是symbol问题，尝试简化测试
                    logger.info("尝试获取服务器时间...")
                    server_time = await asyncio.wait_for(
                        self.exchange.fetch_time(), timeout=30
                    )
                    logger.info(f"服务器时间获取成功: {server_time}")
                
                # 测试账户余额获取 (需要API密钥)
                logger.info("正在测试账户余额获取...")
                try:
                    balance = await asyncio.wait_for(
                        self.exchange.fetch_balance(), timeout=30
                    )
                    logger.info(f"余额获取成功，USDT余额: {balance.get('USDT', {}).get('total', 'N/A')}")
                except Exception as balance_error:
                    logger.error(f"余额获取失败: {balance_error}")
                    logger.warning("余额获取失败，但这可能是API权限问题，继续运行...")
                
                logger.info("API连接测试完成！")
                return True
                
            except asyncio.TimeoutError:
                logger.error(f"连接测试超时 (尝试 {attempt + 1}/{max_retries})")
            except ccxt_async.AuthenticationError as e:
                logger.error(f"API密钥认证失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error("请检查API密钥和密钥权限设置")
            except (ccxt_async.NetworkError, ccxt_async.RequestTimeout) as e:
                logger.error(f"网络连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if self.config.get('use_proxy'):
                    logger.error("请检查代理服务器是否正常运行")
            except Exception as e:
                logger.error(f"连接测试失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error(f"详细错误信息: {type(e).__name__}: {str(e)}")
            
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
        
        logger.error("所有连接测试尝试均失败")
        return False

    # --------------------------------------------------------------------------
    # --- 启动与生命周期管理 ---
    # --------------------------------------------------------------------------
    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        
        try:
            if not await self.test_connection(): raise Exception("API连接测试失败")
            
            logger.info("正在加载市场数据...")
            await self.exchange.load_markets(True)
            if self.symbol not in self.exchange.markets: raise Exception(f"交易对 {self.symbol} 不存在")
            self.market = self.exchange.market(self.symbol)
            logger.info(f"成功加载市场数据，合约精度: {self.market['precision']}")
            
            await self.set_hedge_mode()
            await self.set_leverage()
            await self.full_state_sync()
            
            if self.last_price == 0: self.last_price = (await self.exchange.fetch_ticker(self.symbol))['last']
            
            await self.update_price_series()
            
            self.listen_key = await self.fetch_listen_key()
            asyncio.create_task(self.keep_listen_key_alive())
            
            logger.info("初始化设置完成。")
            logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}")
            
        except Exception as e:
            logger.error(f"初始化设置失败: {e}", exc_info=True)
            raise

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        await self.setup()
        await self.cancel_all_open_orders()
        logger.info("所有现有挂单已取消，准备启动策略...")
        
        while not self.is_shutting_down:
            try:
                stream_url = self.websocket_base_url + f"/ws/{self.listen_key}"
                logger.info(f"正在连接WebSocket: {stream_url}")
                
                async with websockets.connect(stream_url, ping_interval=20, ping_timeout=20) as websocket:
                    logger.info("WebSocket 连接成功。")
                    
                    # 订阅ticker数据流
                    ticker_payload = {"method": "SUBSCRIBE", "params": [f"{self.market['id'].lower()}@bookTicker"], "id": 1}
                    await websocket.send(json.dumps(ticker_payload))
                    logger.info(f"已发送Ticker订阅请求: {ticker_payload}")
                    
                    await self.full_state_sync() # 连接成功后立即同步一次
                    
                    while not self.is_shutting_down:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=60)
                            data = json.loads(message)
                            if 'e' in data: await self.handle_websocket_message(data)
                        except asyncio.TimeoutError:
                            logger.debug("WebSocket接收消息超时，连接可能已断开，将重连。")
                            break
                        except websockets.exceptions.ConnectionClosed as e:
                            logger.warning(f"WebSocket连接已关闭 ({e.code}): {e.reason}")
                            break
            
            except Exception as e:
                logger.error(f"WebSocket主循环发生错误: {e}", exc_info=True)
            
            if self.is_shutting_down: break
            
            logger.info(f"等待 {self.config['system']['connection_retry_delay']} 秒后重连...")
            await asyncio.sleep(self.config['system']['connection_retry_delay'])
            try:
                self.listen_key = await self.fetch_listen_key() # 每次重连都刷新listen key
            except Exception as lk_e:
                logger.error(f"重连时获取ListenKey失败: {lk_e}")


    async def close(self):
        """优雅关闭程序，取消所有挂单并关闭连接"""
        if self.is_shutting_down: return
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        
        # 尝试取消挂单，但不要因为失败而阻止程序关闭
        try:
            if self.exchange and hasattr(self, 'market') and self.market:
                await asyncio.wait_for(self.cancel_all_open_orders(), timeout=10)
        except Exception as e:
            logger.warning(f"关闭时取消挂单失败，但程序将继续关闭: {e}")
        
        # 关闭交易所连接
        try:
            if self.exchange:
                await self.exchange.close()
        except Exception as e:
            logger.warning(f"关闭交易所连接时出错: {e}")
            
        logger.info("程序已关闭。")

    # --------------------------------------------------------------------------
    # --- 核心交易逻辑 ---
    # --------------------------------------------------------------------------
    async def handle_websocket_message(self, data):
        """处理来自WebSocket的消息，分发到不同的处理器"""
        event_type = data.get("e")
        if event_type == "bookTicker":
            if time.time() * 1000 - self.last_ticker_time > self.config['system']['ticker_update_interval_ms']:
                self.last_ticker_time = time.time() * 1000
                await self.handle_ticker_update(data)
        elif event_type == "ORDER_TRADE_UPDATE":
            await self.handle_order_update(data)
        elif event_type == "listenKeyExpired":
            logger.warning("ListenKey 已过期，主循环将自动重连。")
            raise websockets.exceptions.ConnectionClosed(1000, "ListenKey expired")

    async def handle_ticker_update(self, data):
        """处理行情更新并触发策略调整"""
        self.best_bid_price = float(data['b'])
        self.best_ask_price = float(data['a'])
        self.last_price = (self.best_bid_price + self.best_ask_price) / 2
        
        if time.time() - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
            await self.full_state_sync()
            await self.update_price_series()

        await self.execute_argm_strategy()

    async def execute_argm_strategy(self):
        """ARGM V6.0 核心策略执行入口"""
        async with self.lock:
            if self.is_paused and time.time() < self.pause_end_time:
                logger.debug(f"策略暂停中，剩余 {self.pause_end_time - time.time():.0f} 秒")
                return
            elif self.is_paused:
                logger.info("暂停期结束，恢复策略运行。")
                self.is_paused = False
                self.consecutive_losses = 0

            grid_state = self.generate_grid_state(self.price_series, self.current_grids)
            
            if grid_state.should_repaint:
                logger.info(f"网格重绘触发！新中枢: {grid_state.center:.4f}, 新范围: [{grid_state.low:.4f}, {grid_state.high:.4f}]")
                self.current_grids = grid_state.grids
                await self.rebalance_grid_orders(self.current_grids)
            else:
                await self.rebalance_grid_orders(self.current_grids)

    def generate_grid_state(self, price_series, old_grids):
        """单一决策源：计算并决定网格的最终形态"""
        cfg = self.config['argm']
        
        try:
            anchor = price_series['close'].ewm(span=cfg['ema_anchor_period'], adjust=False).mean().iloc[-1]
            
            high_low = price_series['high'] - price_series['low']
            high_close = abs(price_series['high'] - price_series['close'].shift())
            low_close = abs(price_series['low'] - price_series['close'].shift())
            tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = tr.ewm(alpha=1/cfg['atr_period'], adjust=False).mean().iloc[-1]

            delta = price_series['close'].diff()
            gain = (delta.where(delta > 0, 0)).ewm(alpha=1/cfg['rsi_period'], adjust=False).mean()
            loss = (-delta.where(delta < 0, 0)).ewm(alpha=1/cfg['rsi_period'], adjust=False).mean()
            rs = gain / loss
            rsi = (100 - (100 / (1 + rs))).iloc[-1]

            rsi_normalized = (rsi - 50) / 50
            volatility_factor = 2.5 + abs(rsi_normalized) * 1.5

            grid_low = anchor - (atr * volatility_factor)
            grid_high = anchor + (atr * volatility_factor)

            new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
            
            overlap_ratio = self.calculate_grid_overlap(new_grids, old_grids)
            should_repaint_flag = overlap_ratio < cfg['repaint_overlap_ratio'] if old_grids else True

            return self.GridState(
                grids=new_grids, center=anchor, low=grid_low, high=grid_high, should_repaint=should_repaint_flag
            )
        except Exception as e:
            logger.error(f"生成网格状态失败: {e}", exc_info=True)
            return self.GridState(grids=old_grids, center=0, low=0, high=0, should_repaint=False)


    def get_trading_direction(self):
        """根据配置决定交易方向"""
        override = self.config['argm']['trend_override'].upper()
        if override in ["LONG_ONLY", "SHORT_ONLY"]: return override
        
        ema_trend = self.price_series['close'].ewm(span=self.config['argm']['ema_trend_period'], adjust=False).mean().iloc[-1]
        return "LONG_ONLY" if self.last_price > ema_trend else "SHORT_ONLY"

    def get_next_order_size(self):
        """统一决定下一次交易的仓位大小"""
        base_size_usd = self.config['initial_value']
        risk_cfg = self.config['risk_management']['smart_recovery_config']
        reinv_cfg = self.config['risk_management']['reinvest_config']

        if self.consecutive_losses >= risk_cfg['pause_threshold']:
            if not self.is_paused:
                self.is_paused = True
                kline_interval_minutes = int(self.config['argm']['kline_timeframe'][:-1])
                self.pause_end_time = time.time() + risk_cfg['pause_bars'] * kline_interval_minutes * 60
                logger.warning(f"连续亏损达到 {self.consecutive_losses} 次，暂停交易 {risk_cfg['pause_bars'] * kline_interval_minutes} 分钟。")
            return base_size_usd
        elif self.consecutive_losses == 2: return base_size_usd * risk_cfg['reduce_multiplier']
        elif self.consecutive_losses == 1: return base_size_usd * risk_cfg['add_multiplier']
        
        if self.total_profit_usd > reinv_cfg['threshold_usd']:
            profit_ratio = self.total_profit_usd / (self.config.get('total_capital', 10000)) # 可配置总本金
            reinvested_size = base_size_usd * (1 + profit_ratio * reinv_cfg['profit_ratio_multiplier'])
            return min(reinvested_size, base_size_usd * reinv_cfg['size_cap_multiplier'])
        
        return base_size_usd

    # --------------------------------------------------------------------------
    # --- 订单与状态管理 ---
    # --------------------------------------------------------------------------
    async def rebalance_grid_orders(self, target_grids):
        """对比目标网格与现有订单，执行新增、删除操作"""
        if not target_grids or self.last_price <= 0: return

        direction = self.get_trading_direction()
        order_size_usd = self.get_next_order_size()
        order_quantity = order_size_usd / self.last_price
        
        # 1. 识别需要下的目标订单
        orders_to_place = []
        existing_prices = {o['price'] for o in self.open_orders.values()}
        
        for price in target_grids:
            if price not in existing_prices:
                if direction == "LONG_ONLY" and price < self.best_ask_price:
                    orders_to_place.append({'side': 'buy', 'price': price, 'positionSide': 'LONG'})
                elif direction == "SHORT_ONLY" and price > self.best_bid_price:
                    orders_to_place.append({'side': 'sell', 'price': price, 'positionSide': 'SHORT'})
        
        # 2. 识别需要取消的多余订单
        orders_to_cancel = [o for o in self.open_orders.values() if o['price'] not in target_grids]

        # --- 执行操作 ---
        if orders_to_cancel:
            cancel_tasks = [self.cancel_order(o['clientOrderId']) for o in orders_to_cancel]
            await asyncio.gather(*cancel_tasks, return_exceptions=True)

        if orders_to_place:
            create_tasks = [
                self.place_order(t['side'], t['positionSide'], order_quantity, t['price']) for t in orders_to_place
            ]
            await asyncio.gather(*create_tasks, return_exceptions=True)

    async def handle_order_update(self, data):
        """处理订单更新消息，并更新风控状态"""
        order_data = data.get("o", {})
        if order_data.get("s") != self.market['id']: return

        async with self.lock:
            status = order_data['X']
            client_order_id = order_data['c']

            if status in ["CANCELED", "EXPIRED", "REJECTED"]:
                if client_order_id in self.open_orders: del self.open_orders[client_order_id]
                logger.info(f"订单终结 ({status}): ID: {client_order_id}")

            elif status in ["FILLED", "PARTIALLY_FILLED"]:
                side = order_data['S']
                position_side = order_data['ps']
                filled_qty = float(order_data['l'])
                filled_price = float(order_data['L'])
                
                logger.info(f"订单成交: {side} {position_side} {filled_qty:.4f} @ {filled_price:.4f}")

                # 这是一个简化的利润计算，实际中需要更精确的配对逻辑
                is_closing_order = (side == 'SELL' and position_side == 'LONG') or (side == 'BUY' and position_side == 'SHORT')
                if is_closing_order:
                    entry_price = float(order_data.get('ap', filled_price)) # 使用均价
                    profit_per_contract = (filled_price - entry_price) if side == 'SELL' else (entry_price - filled_price)
                    profit = profit_per_contract * filled_qty
                    self.total_profit_usd += profit
                    self.consecutive_losses = 0
                    logger.info(f"一轮套利完成，利润: {profit:.2f} USD. 累计利润: {self.total_profit_usd:.2f}")
                else: # 开仓单成交
                    self.consecutive_losses = 0

                if status == "FILLED" and client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]
                
                # 成交后立即同步状态，确保持仓准确
                asyncio.create_task(self.full_state_sync())

    async def place_order(self, side, position_side, amount, price):
        """封装统一的下单请求"""
        if amount < self.market['limits']['amount']['min']: return
        
        client_order_id = f"x-argm-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
        params = {'positionSide': position_side.upper(), 'newClientOrderId': client_order_id}
        
        try:
            logger.info(f"准备下单: {side} {position_side} {amount:.4f} @ {price:.4f}")
            new_order = await self.exchange.create_order(self.symbol, 'limit', side, amount, price, params)
            if new_order and 'clientOrderId' in new_order:
                self.open_orders[new_order['clientOrderId']] = new_order
        except Exception as e:
            logger.error(f"下单失败: {side} {amount} @ {price}, 原因: {e}")

    async def cancel_order(self, client_order_id):
        """根据客户端订单ID取消订单"""
        order_to_cancel = self.open_orders.get(client_order_id)
        if not order_to_cancel: return
        
        try:
            exchange_order_id = order_to_cancel['id']
            await self.exchange.cancel_order(exchange_order_id, self.symbol)
            logger.info(f"订单已请求撤销: ClientID={client_order_id}")
        except Exception as e:
            if any(err in str(e).lower() for err in ["order does not exist", "already filled", "unknown order"]):
                if client_order_id in self.open_orders: del self.open_orders[client_order_id]
            else:
                logger.error(f"撤单失败 (ClientID: {client_order_id}): {e}")
    
    async def cancel_all_open_orders(self):
        """取消所有挂单"""
        try:
            # 只有在正常运行状态下才同步状态
            if not self.is_shutting_down and hasattr(self, 'market') and self.market:
                await self.full_state_sync()
            
            if self.open_orders:
                logger.info(f"正在取消 {len(self.open_orders)} 个挂单...")
                await self.exchange.cancel_all_orders(self.symbol)
                self.open_orders.clear()
                logger.info("所有挂单已取消")
            else:
                # 如果没有本地记录的挂单，尝试直接取消所有挂单
                try:
                    await self.exchange.cancel_all_orders(self.symbol)
                    logger.info("已尝试取消所有挂单")
                except Exception:
                    pass  # 忽略取消挂单的错误
        except Exception as e:
            logger.error(f"批量撤单失败: {e}", exc_info=True)

    async def full_state_sync(self):
        """通过REST API完全同步持仓和挂单状态"""
        async with self.lock:
            try:
                positions_f = self.exchange.fetch_positions([self.symbol])
                orders_f = self.exchange.fetch_open_orders(self.symbol)
                ticker_f = self.exchange.fetch_ticker(self.symbol)
                
                positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)

                self.last_price = ticker['last']
                
                long_pos = next((p for p in positions if p['info']['positionSide'] == 'LONG'), None)
                short_pos = next((p for p in positions if p['info']['positionSide'] == 'SHORT'), None)
                self.long_position_size = float(long_pos['contracts']) if long_pos else 0.0
                self.short_position_size = float(short_pos['contracts']) if short_pos else 0.0
                self.net_inventory = self.long_position_size - self.short_position_size
                
                self.open_orders = {o['clientOrderId']: o for o in open_orders if 'clientOrderId' in o}
                self.last_state_sync_time = time.time()
                logger.debug(f"状态同步完成。持仓: L={self.long_position_size:.4f}, S={self.short_position_size:.4f}. 挂单: {len(self.open_orders)}")
            except Exception as e:
                logger.error(f"状态完全同步失败: {e}", exc_info=True)

    # --------------------------------------------------------------------------
    # --- 辅助函数 ---
    # --------------------------------------------------------------------------
    async def update_price_series(self):
        """更新用于计算指标的K线数据"""
        try:
            cfg = self.config['argm']
            periods_needed = [k for k in ['ema_trend_period', 'ema_anchor_period', 'atr_period', 'rsi_period']]
            limit = max(cfg[p] for p in periods_needed) + 5
            
            ohlcv = await self.exchange.fetch_ohlcv(self.symbol, cfg['kline_timeframe'], limit=limit)
            self.price_series = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            logger.debug(f"K线数据已更新，共 {len(self.price_series)} 条记录。")
        except Exception as e:
            logger.error(f"更新K线数据失败: {e}")

    def generate_nonlinear_grids(self, center, low, high, num_levels):
        """非线性网格生成"""
        if high <= low: return []
        # 使用sigmoid函数创建在中心密集的点
        points = np.linspace(-5, 5, num_levels)
        sigmoid_points = 1 / (1 + np.exp(-points))
        # 将sigmoid输出 [0,1] 缩放到 [low, high]
        prices = low + (high - low) * sigmoid_points
        
        price_precision = self.market['precision']['price']
        if price_precision is not None and price_precision > 0:
            decimals = int(-math.log10(price_precision))
        else:
            # 如果精度不可用，提供一个默认值
            decimals = 4
        
        return sorted(list(set(np.round(prices, decimals))))

    def calculate_grid_overlap(self, new_grids, old_grids):
        """计算新旧网格重合度"""
        if not old_grids or not new_grids: return 0.0
        old_set = set(old_grids)
        new_set = set(new_grids)
        return len(old_set.intersection(new_set)) / len(old_set)

    async def set_hedge_mode(self):
        """设置账户为双向持仓模式"""
        try:
            await self.exchange.fapiPrivatePostPositionSideDual({"dualSidePosition": "true"})
            logger.info("双向持仓模式已成功设置。")
        except Exception as e:
            if any(err in str(e).lower() for err in ["no need to change", "-4059"]):
                logger.info("当前已是双向持仓模式，无需更改。")
            else: logger.error(f"设置双向持仓模式失败: {e}")

    async def set_leverage(self):
        """设置杠杆倍数"""
        try:
            await self.exchange.set_leverage(self.config['leverage'], self.symbol)
            logger.info(f"杠杆已设置为 {self.config['leverage']}x")
        except Exception as e:
            if 'leverage not modified' in str(e).lower():
                logger.info(f"杠杆已经是 {self.config['leverage']}x，无需更改")
            else: logger.error(f"设置杠杆失败: {e}")

    async def fetch_listen_key(self):
        """获取WebSocket监听密钥"""
        try:
            logger.info("正在获取新的Listen Key...")
            response = await self.exchange.fapiPrivatePostListenKey()
            key = response.get('listenKey')
            if not key: raise ValueError("获取的 listenKey 为空")
            logger.info(f"成功获取新的 listen key: {key[:10]}...")
            return key
        except Exception as e:
            logger.error(f"获取 listen key 失败: {e}", exc_info=True)
            raise

    async def keep_listen_key_alive(self):
        """后台任务，定期延长 listenKey 的有效期"""
        while not self.is_shutting_down:
            await asyncio.sleep(1800) # 每30分钟
            try:
                if self.listen_key:
                    logger.info("正在延长 ListenKey...")
                    await self.exchange.fapiPrivatePutListenKey({'listenKey': self.listen_key})
                    logger.info("ListenKey 已成功延长。")
            except Exception as e:
                logger.error(f"延长 listen key 失败: {e}")


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    """主程序入口函数"""
    bot = None
    try:
        # 参考 maker_BN_new.py, 增加启动前的API连接预检
        logger.info("启动前预检 API 连接...")
        proxy = CONFIG.get('proxy_url') if CONFIG.get('use_proxy') else None
        ping_url = "https://fapi.binance.com/fapi/v1/ping"
        
        # 检查代理是否可用
        if proxy:
            logger.info(f"检测到代理配置: {proxy}")
            try:
                # 先测试代理本身是否可用
                timeout = aiohttp.ClientTimeout(total=10)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 使用更可靠的测试URL
                    test_urls = ["http://httpbin.org/ip", "http://www.google.com", "http://www.baidu.com"]
                    proxy_working = False
                    for test_url in test_urls:
                        try:
                            async with session.get(test_url, proxy=proxy) as response:
                                if response.status == 200:
                                    logger.info(f"代理服务器连接正常 (测试URL: {test_url})")
                                    proxy_working = True
                                    break
                        except Exception as url_e:
                            logger.debug(f"测试URL {test_url} 失败: {url_e}")
                            continue

                    if not proxy_working:
                        logger.warning("代理服务器可能有问题，但继续尝试...")

            except Exception as e:
                logger.error(f"代理服务器连接失败: {e}")
                logger.error("请检查代理服务器是否正在运行且配置正确")
                logger.info("尝试继续运行，但可能会失败...")
                # 不要直接返回，让程序继续尝试
        
        # 测试API端点
        max_retries = 3
        api_test_passed = False
        for attempt in range(max_retries):
            try:
                timeout = aiohttp.ClientTimeout(total=30) # 增加超时时间
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    logger.info(f"正在测试 ping 端点: {ping_url} (代理: {proxy or '无'}) - 尝试 {attempt + 1}/{max_retries}")
                    start_time = time.time()
                    async with session.get(ping_url, proxy=proxy) as response:
                        elapsed = time.time() - start_time
                        if response.status == 200:
                            logger.info(f"API 端点预检成功, 响应码: {response.status}, 耗时: {elapsed:.2f}s")
                            api_test_passed = True
                            break
                        else:
                            logger.error(f"API 端点预检失败, 响应码: {response.status}, 耗时: {elapsed:.2f}s")
                            if attempt == max_retries - 1:
                                logger.warning("API端点返回非200状态码，但继续尝试运行...")
            except Exception as e:
                logger.error(f"API 连接预检失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                logger.error(f"错误类型: {type(e).__name__}")
                if attempt < max_retries - 1:
                    logger.info("等待5秒后重试...")
                    await asyncio.sleep(5)
                    continue
                else:
                    logger.warning("所有API连接预检尝试均失败，但继续尝试运行...")
                    if proxy:
                        logger.warning("请检查代理服务器是否正在运行且配置正确。")
                    else:
                        logger.warning("请检查您的网络连接。")
                    # 不要直接退出，让程序继续尝试

        if not api_test_passed:
            logger.warning("API预检失败，但程序将继续运行。如果后续连接失败，请检查网络和代理设置。")

        logger.info("启动 ARGM-V6.0 'Singularity' 策略...")
        bot = ARGMStrategyBot(CONFIG)
        await bot.run()
    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            await bot.close()
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())