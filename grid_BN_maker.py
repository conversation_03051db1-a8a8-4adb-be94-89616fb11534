# advanced_grid_bot_integrated.py

import asyncio
import websockets
import json
import logging
import hmac
import hashlib
import time
import ccxt.async_support as ccxt # 明确使用异步版本的ccxt
import math
import os
import sys
import uuid
import pandas as pd # 用于计算技术指标

# ==================== 配置 (全部整合于此) ====================
CONFIG = {
    "api_key": "vfP78BkDu0lec7juXjS6vqt9HnXPYwkj6EQmWM1N61j92gc1Y5rjWFmN14DTVdn9",      # 替换为你的 API Key
    "api_secret": "F4n5LfEsXczeNpNOMpU2poQdd6WRPKAW0XhxRsn1JhvtUapA0OwpffisciKkl3v5",  # 替换为你的 API Secret
    "use_proxy": True,
    "proxy_url": "http://127.0.0.1:7897",
    
    # --- 策略核心参数 ---
    "coin_name": "TRX",
    "contract_type": "USDC",
    "leverage": 10,
    "initial_value": 100,  # 单笔订单的名义价值 (USDT/USDC)

    # --- 网格与报价逻辑 ---
    "grid": {
        "dynamic_spacing_enabled": True,       # 是否启用基于ATR的动态间距
        "atr_period": 14,                      # ATR计算周期
        "atr_multiplier": 0.5,                 # ATR波动率乘数，越大网格越宽
        "base_spacing_pct": 0.0015,            # 如果禁用动态间距，使用的固定间距 (0.15%)
        "fair_price_ema_period": 20            # 计算公允价值的EMA周期
    },

    # --- 风险与库存管理 ---
    "risk_management": {
        "position_limit_contracts": 10,        # 净头寸合约数量的硬性上限
        "inventory_skew_factor": 0.0005,       # 库存偏斜因子，越大，库存对报价影响越大
        "rebalance_threshold_pct": 0.25,       # 价格偏离多少比例时调整订单 (网格间距的25%)
        "target_inventory": 0                  # 目标净库存 (可设为非0以套取资金费)
    },

    # --- 系统参数 ---
    "system": {
        "sync_interval_seconds": 60,           # REST API状态完全同步的间隔
        "websocket_url": "wss://fstream.binance.com/ws",
        "timeout_seconds": 60                  # 增加超时时间到60秒
    }
}

# ==================== 日志配置 ====================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()

# ==================== 网格交易机器人 ====================
class AdvancedGridBot:
    def __init__(self, config):
        self.config = config
        self.api_key = config['api_key']
        self.api_secret = config['api_secret']
        
        self.exchange = None # 将在异步运行时初始化
        self.lock = asyncio.Lock()

        # --- 策略状态 ---
        self.symbol = f"{config['coin_name']}/{config['contract_type']}:{config['contract_type']}"
        self.market = None
        
        # 实时数据
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0
        self.fair_price = 0.0
        
        # 持仓和订单状态 (单一事实来源)
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {} # key: clientOrderId, value: order_object from ws or rest

        # 策略参数
        self.quantity = 0.0
        self.grid_spacing = 0.0
        
        # 控制变量
        self.listen_key = None
        self.last_rebalance_time = 0
        self.last_state_sync_time = 0
        self.last_ticker_time = 0

    async def initialize(self):
        """异步初始化交易所和其他需要await的操作。"""
        self.exchange = await self._initialize_exchange()
        if not self.exchange:
            logger.error("交易所初始化失败，程序退出。")
            return False
            
        try:
            await self.setup()
            return True
        except Exception as e:
            logger.error(f"初始化设置失败: {e}", exc_info=True)
            if self.exchange:
                await self.exchange.close()
            return False

    async def _initialize_exchange(self):
        if not self.api_key or not self.api_secret or self.api_key == "YOUR_API_KEY":
            logger.error("API密钥未配置，请在CONFIG中设置")
            return None
            
        options = {
            "defaultType": "future", 
            "timeout": self.config['system']['timeout_seconds'] * 1000,
            "recvWindow": 10000,
        }
        
        proxies = {'http': self.config['proxy_url'], 'https': self.config['proxy_url']} if self.config['use_proxy'] else None
            
        exchange = ccxt.binance({
            "apiKey": self.api_key,
            "secret": self.api_secret,
            "options": options,
            "proxies": proxies,
            "enableRateLimit": True,
        })
        
        try:
            logger.info("正在连接交易所并加载市场数据...")
            await exchange.load_markets()
            self.market = exchange.market(self.symbol)
            logger.info("交易所API连接成功，市场数据已加载")
            return exchange
        except Exception as e:
            logger.error(f"连接交易所失败: {e}", exc_info=True)
            if exchange:
                await exchange.close()
            return None

    async def setup(self):
        logger.info("正在执行启动设置...")
        
        await self.set_hedge_mode()
        await self.set_leverage(self.config['leverage'])
        
        ticker = await self.exchange.fetch_ticker(self.symbol)
        self.last_price = ticker['last']
        
        await self.full_state_sync()
        await self.update_dynamic_parameters()
        
        self.listen_key = await self.fetch_listen_key()
        if not self.listen_key:
            raise Exception("获取ListenKey失败，无法继续")
            
        asyncio.create_task(self.keep_listen_key_alive())
        
        logger.info("初始化设置完成。")
        logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}, 净库存={self.net_inventory}")
        logger.info(f"初始下单量={self.quantity}, 初始网格间距={self.grid_spacing:.4f}")

    async def run(self):
        await self.cancel_all_open_orders()
        
        while True:
            try:
                symbol_lower = self.market['id'].lower()
                stream_url = f"{self.config['system']['websocket_url']}/stream?streams={symbol_lower}@bookTicker/{self.listen_key}"

                async with websockets.connect(stream_url) as websocket:
                    logger.info("WebSocket 连接成功，已订阅数据流。")
                    while True:
                        message = await websocket.recv()
                        data = json.loads(message)
                        
                        if 'data' in data:
                            await self.handle_websocket_message(data['data'])
                        else:
                            logger.debug(f"收到其他类型消息: {data}")
                            
            except (websockets.exceptions.ConnectionClosed, asyncio.TimeoutError) as e:
                logger.error(f"WebSocket 连接中断: {e}. 5秒后重连...")
                await asyncio.sleep(5)
            except Exception as e:
                logger.critical(f"主循环发生严重错误: {e}", exc_info=True)
                await asyncio.sleep(15)

    async def handle_websocket_message(self, data):
        event_type = data.get("e")
        if event_type == "bookTicker":
            if time.time() - self.last_ticker_time < 0.2:
                return
            await self.handle_ticker_update(data)
            self.last_ticker_time = time.time()
        elif event_type == "ORDER_TRADE_UPDATE":
            await self.handle_order_update(data)
        elif event_type == "listenKeyExpired":
            logger.warning("ListenKey 已过期，正在重新获取...")
            self.listen_key = await self.fetch_listen_key()
            if not self.listen_key:
                logger.error("无法重新获取ListenKey，程序可能无法正常工作。")
            raise websockets.exceptions.ConnectionClosed(1000, "ListenKey expired, must reconnect.")

    async def handle_ticker_update(self, data):
        self.best_bid_price = float(data['b'])
        self.best_ask_price = float(data['a'])
        self.last_price = (self.best_bid_price + self.best_ask_price) / 2

        if time.time() - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
            await self.full_state_sync()

        await self.adjust_grid_strategy()

    async def handle_order_update(self, data):
        order_data = data.get("o", {})
        if order_data.get("s") != self.market['id']:
            return
            
        async with self.lock:
            client_order_id = order_data['c']
            status = order_data['X']
            
            if status == "NEW":
                self.open_orders[client_order_id] = order_data
                logger.info(f"新订单: {order_data['S']} {order_data['ps']} {order_data['q']} @ {order_data['p']}, ID: {client_order_id}")
            
            elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                if client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]
                    logger.info(f"订单终结 ({status}): ID: {client_order_id}")

            elif status == "FILLED" or (status == "PARTIALLY_FILLED" and float(order_data['l']) > 0):
                filled_qty = float(order_data['l'])
                position_side = order_data['ps']
                side = order_data['S']

                if position_side == 'LONG':
                    self.long_position_size += filled_qty if side == 'BUY' else -filled_qty
                elif position_side == 'SHORT':
                    self.short_position_size += filled_qty if side == 'SELL' else -filled_qty
                
                self.long_position_size = max(0, self.long_position_size)
                self.short_position_size = max(0, self.short_position_size)
                
                self.net_inventory = self.long_position_size - self.short_position_size
                logger.info(f"订单成交: {side} {position_side} {filled_qty}. 最新持仓: 多={self.long_position_size:.4f}, 空={self.short_position_size:.4f}, 净库存={self.net_inventory:.4f}")
                
                if order_data['X'] == "FILLED" and client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]

    async def adjust_grid_strategy(self):
        await self.update_dynamic_parameters()
        
        inventory_risk = self.net_inventory * self.config['risk_management']['inventory_skew_factor']
        skewed_mid_price = self.fair_price * (1 - inventory_risk)
        
        target_bid_price = skewed_mid_price * (1 - self.grid_spacing)
        target_ask_price = skewed_mid_price * (1 + self.grid_spacing)

        if abs(self.net_inventory) >= self.config['risk_management']['position_limit_contracts']:
            logger.warning(f"净库存 {self.net_inventory} 达到限制。暂停开新仓，将尝试平仓。")
            if self.net_inventory > 0:
                await self.manage_orders(bid_price=None, ask_price=target_ask_price)
            else:
                await self.manage_orders(bid_price=target_bid_price, ask_price=None)
            return
        
        await self.manage_orders(bid_price=target_bid_price, ask_price=target_ask_price)

    async def manage_orders(self, bid_price, ask_price):
        target_orders = []
        if bid_price:
            target_orders.append({'side': 'buy', 'price': bid_price, 'positionSide': 'LONG'})
        if ask_price:
            target_orders.append({'side': 'sell', 'price': ask_price, 'positionSide': 'SHORT'})

        current_orders = list(self.open_orders.values())
        
        matched_targets = []
        for order in current_orders:
            order_side = order['S'].lower()
            order_ps = order['ps']
            
            match_found = False
            for target in target_orders:
                if target['side'] == order_side and target['positionSide'] == order_ps:
                    price_diff = abs(float(order['p']) - target['price']) / target['price']
                    rebalance_threshold = self.grid_spacing * self.config['risk_management']['rebalance_threshold_pct']
                    if price_diff > rebalance_threshold:
                        logger.info(f"调整订单: {order_side} {order_ps}, 旧价:{order['p']}, 新价:{target['price']:.4f}")
                        await self.cancel_and_replace(order, target)
                    
                    matched_targets.append(target)
                    match_found = True
                    break
            
            if not match_found:
                logger.info(f"取消多余订单: {order_side} {order_ps} @ {order['p']}")
                await self.cancel_order(order['c'])

        for target in target_orders:
            if target not in matched_targets:
                logger.info(f"创建缺失订单: {target['side']} {target['positionSide']} @ {target['price']:.4f}")
                await self.place_order(target['side'], target['positionSide'], self.quantity, target['price'])

    async def cancel_and_replace(self, old_order, new_target):
        await self.cancel_order(old_order['c'])
        await self.place_order(new_target['side'], new_target['positionSide'], self.quantity, new_target['price'])

    async def update_dynamic_parameters(self):
        if self.last_price > 0:
            self.quantity = self.config['initial_value'] / self.last_price
            self.quantity = float(self.exchange.amount_to_precision(self.symbol, self.quantity))
        
        if self.config['grid']['dynamic_spacing_enabled']:
            try:
                ohlcv = await self.exchange.fetch_ohlcv(self.symbol, '1m', limit=self.config['grid']['atr_period'] + 1)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift())
                low_close = abs(df['low'] - df['close'].shift())
                tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = tr.rolling(window=self.config['grid']['atr_period']).mean().iloc[-1]
                
                self.grid_spacing = (atr / self.last_price) * self.config['grid']['atr_multiplier']
                ema_period = self.config['grid']['fair_price_ema_period']
                self.fair_price = df['close'].ewm(span=ema_period, adjust=False).mean().iloc[-1]
            except Exception as e:
                logger.warning(f"计算动态参数失败，使用固定值: {e}")
                self.grid_spacing = self.config['grid']['base_spacing_pct']
                self.fair_price = self.last_price
        else:
            self.grid_spacing = self.config['grid']['base_spacing_pct']
            self.fair_price = self.last_price

    async def full_state_sync(self):
        async with self.lock:
            try:
                logger.info("正在执行状态完全同步...")
                positions_task = self.exchange.fetch_positions([self.symbol])
                orders_task = self.exchange.fetch_open_orders(self.symbol)
                positions, open_orders_raw = await asyncio.gather(positions_task, orders_task)

                long_pos = next((p for p in positions if p['side'] == 'long'), None)
                short_pos = next((p for p in positions if p['side'] == 'short'), None)
                self.long_position_size = float(long_pos['contracts']) if long_pos else 0.0
                self.short_position_size = float(short_pos['contracts']) if short_pos else 0.0
                self.net_inventory = self.long_position_size - self.short_position_size

                self.open_orders = {o['clientOrderId']: o['info'] for o in open_orders_raw if 'clientOrderId' in o}
                
                self.last_state_sync_time = time.time()
                logger.info(f"状态同步完成。持仓: L={self.long_position_size}, S={self.short_position_size}. 挂单数: {len(self.open_orders)}")
            except Exception as e:
                logger.error(f"状态完全同步失败: {e}", exc_info=True)

    # --- 交易所交互辅助函数 ---
    async def set_hedge_mode(self):
        try:
            position_mode = await self.exchange.fapiPrivateGetPositionSideDual()
            is_dual = position_mode.get('dualSidePosition', False)
            if not is_dual:
                logger.info("当前不是双向持仓模式，正在尝试启用...")
                await self.exchange.fapiPrivatePostPositionSideDual({'dualSidePosition': 'true'})
                logger.info("双向持仓模式已启用。")
            else:
                logger.info("当前已是双向持仓模式。")
        except Exception as e:
            if 'No need to change position side' in str(e):
                logger.info("当前已是双向持仓模式。")
            else:
                logger.error(f"设置双向持仓模式失败: {e}", exc_info=True)
                raise

    async def set_leverage(self, leverage):
        try:
            await self.exchange.set_leverage(leverage, self.symbol)
            logger.info(f"杠杆已设置为 {leverage}x")
        except Exception as e:
            logger.error(f"设置杠杆失败: {e}", exc_info=True)
            raise

    async def place_order(self, side, position_side, amount, price):
        if amount <= float(self.market['limits']['amount']['min']):
            logger.warning(f"订单数量 {amount} 小于最小下单量，跳过下单。")
            return
        try:
            client_order_id = f"x-grid-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
            params = {'positionSide': position_side.upper(), 'newClientOrderId': client_order_id}
            
            await self.exchange.create_order(self.symbol, 'limit', side, amount, price, params)
        except Exception as e:
            logger.error(f"下单失败: {side} {amount} @ {price}, 原因: {e}", exc_info=True)

    async def cancel_order(self, client_order_id):
        try:
            order_to_cancel = self.open_orders.get(client_order_id)
            if order_to_cancel:
                await self.exchange.cancel_order(order_to_cancel['i'], self.symbol)
        except ccxt.OrderNotFound:
             logger.warning(f"尝试取消一个不存在或已处理的订单: {client_order_id}")
        except Exception as e:
            logger.error(f"撤单失败 (ID: {client_order_id}): {e}", exc_info=True)
    
    async def cancel_all_open_orders(self):
        try:
            logger.info(f"准备撤销所有 {self.symbol} 的挂单...")
            await self.exchange.cancel_all_orders(self.symbol)
            self.open_orders.clear()
            logger.info("所有挂单已撤销。")
        except Exception as e:
            logger.error(f"撤销所有订单失败: {e}", exc_info=True)

    async def fetch_listen_key(self):
        try:
            response = await self.exchange.fapiPrivatePostListenKey()
            logger.info("成功获取ListenKey。")
            return response['listenKey']
        except Exception as e:
            logger.error(f"获取ListenKey失败: {e}", exc_info=True)
            return None

    async def keep_listen_key_alive(self):
        while True:
            await asyncio.sleep(1800)
            try:
                if self.listen_key:
                    await self.exchange.fapiPrivatePutListenKey({'listenKey': self.listen_key})
                    logger.info("ListenKey已续订。")
            except Exception as e:
                logger.error(f"续订ListenKey失败: {e}", exc_info=True)

    async def close(self):
        if self.exchange:
            await self.exchange.close()
            logger.info("交易所连接已关闭。")

# ==================== 主程序 ====================
async def main():
    bot = AdvancedGridBot(CONFIG)
    try:
        if not await bot.initialize():
            logger.error("机器人初始化失败，正在退出。")
            return
            
        await bot.run()
    except KeyboardInterrupt:
        logger.info("程序被用户中断。")
    except Exception as e:
        logger.critical(f"main函数捕获到未处理的异常: {e}", exc_info=True)
    finally:
        logger.info("正在关闭程序...")
        if bot and bot.exchange:
            try:
                await bot.cancel_all_open_orders()
            except Exception:
                pass
            await bot.close()

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())