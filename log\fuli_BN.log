2025-07-17 01:43:07,061 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:43:07,061 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:43:07,070 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:43:07,070 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:43:07,072 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:43:07,072 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:43:07,158 - INFO - [test_connection] - 交易所状态检查成功: {'status': 'ok', 'updated': None, 'eta': None, 'url': None, 'info': {'status': '0', 'msg': 'normal'}}
2025-07-17 01:43:07,822 - INFO - [test_connection] - 成功获取行情数据: 最新价格 172.33
2025-07-17 01:43:07,892 - INFO - [test_connection] - 成功获取账户余额: USDC = 611.43812764
2025-07-17 01:43:07,892 - INFO - [test_connection] - 所有API测试通过
2025-07-17 01:43:07,892 - INFO - [setup] - 正在加载市场数据...
2025-07-17 01:43:08,475 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 01:43:08,542 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改。
2025-07-17 01:43:08,606 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 01:43:08,872 - INFO - [fetch_listen_key] - 正在获取新的Listen Key...
2025-07-17 01:43:08,942 - INFO - [fetch_listen_key] - 成功获取新的 listen key: zQY0aCQW39...
2025-07-17 01:43:08,942 - INFO - [setup] - 初始化设置完成。
2025-07-17 01:43:08,942 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 01:43:08,942 - ERROR - [cancel_all_open_orders] - 批量撤单失败: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 533, in cancel_all_open_orders
    await self.sync_order_status()
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
2025-07-17 01:43:08,944 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 01:43:08,944 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/zQY0aCQW39lN0Pg2uFTanCpwPLgZqnQwV5Dgu4jQP9fPJ2hqX0vIhFP7rhh7d1R5
2025-07-17 01:43:09,142 - INFO - [run] - WebSocket 连接成功。
2025-07-17 01:43:09,143 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdc@bookTicker'], 'id': 1}
2025-07-17 01:43:09,362 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:09,959 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:10,466 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:10,972 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:11,782 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:12,295 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:12,822 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:13,327 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:13,851 - ERROR - [generate_grid_state] - 生成网格状态失败: 'float' object cannot be interpreted as an integer
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 57, in _wrapfunc
    return bound(*args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 378, in generate_grid_state
    new_grids = self.generate_nonlinear_grids(center=anchor, low=grid_low, high=grid_high, num_levels=cfg['grid_total_levels'])
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 593, in generate_nonlinear_grids
    return sorted(list(set(np.round(prices, self.market['precision']['price']))))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 3758, in round
    return _wrapfunc(a, 'round', decimals=decimals, out=out)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 66, in _wrapfunc
    return _wrapit(obj, method, *args, **kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\numpy\_core\fromnumeric.py", line 46, in _wrapit
    result = getattr(arr, method)(*args, **kwds)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'float' object cannot be interpreted as an integer
2025-07-17 01:43:20,135 - INFO - [close] - 正在关闭程序...
2025-07-17 01:43:20,135 - ERROR - [cancel_all_open_orders] - 批量撤单失败: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 657, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 274, in run
    message = await asyncio.wait_for(websocket.recv(), timeout=60)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\tasks.py", line 520, in wait_for
    return await fut
           ^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\websockets\asyncio\connection.py", line 303, in recv
    return await self.recv_messages.get(decode)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\websockets\asyncio\messages.py", line 159, in get
    frame = await self.frames.get(not self.closed)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\websockets\asyncio\messages.py", line 51, in get
    await self.get_waiter
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 533, in cancel_all_open_orders
    await self.sync_order_status()
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ARGMStrategyBot' object has no attribute 'sync_order_status'
2025-07-17 01:43:20,138 - INFO - [close] - 程序已关闭。
2025-07-17 01:44:52,124 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:44:52,124 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:44:52,131 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:44:52,131 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:44:52,133 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:44:52,133 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:44:52,173 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://api.binance.com/sapi/v1/system/status
2025-07-17 01:44:52,173 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 225, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:44:52,174 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 665, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 253, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 225, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:44:52,174 - INFO - [close] - 正在关闭程序...
2025-07-17 01:44:52,999 - INFO - [close] - 程序已关闭。
2025-07-17 01:44:52,999 - INFO - [main] - 程序已完全退出。
2025-07-17 01:46:30,092 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:46:30,092 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:46:30,099 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:46:30,099 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:46:30,101 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:46:30,101 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:46:30,101 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:46:30,187 - INFO - [test_connection] - Futures API连通性检查成功。
2025-07-17 01:46:30,720 - INFO - [test_connection] - 成功获取行情数据: 最新价格 173.64
2025-07-17 01:46:30,783 - INFO - [test_connection] - 成功获取账户余额: USDC = 611.43812764
2025-07-17 01:46:30,783 - INFO - [test_connection] - 所有API测试通过
2025-07-17 01:46:30,783 - INFO - [setup] - 正在加载市场数据...
2025-07-17 01:46:31,262 - INFO - [setup] - 成功加载市场数据，合约精度: {'amount': 0.01, 'price': 0.01, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-17 01:46:31,324 - INFO - [set_hedge_mode] - 当前已是双向持仓模式，无需更改。
2025-07-17 01:46:31,388 - INFO - [set_leverage] - 杠杆已设置为 20x
2025-07-17 01:46:31,654 - INFO - [fetch_listen_key] - 正在获取新的Listen Key...
2025-07-17 01:46:31,720 - INFO - [fetch_listen_key] - 成功获取新的 listen key: zQY0aCQW39...
2025-07-17 01:46:31,720 - INFO - [setup] - 初始化设置完成。
2025-07-17 01:46:31,720 - INFO - [setup] - 当前持仓: 多头=0.0, 空头=0.0
2025-07-17 01:46:31,836 - INFO - [run] - 所有现有挂单已取消，准备启动策略...
2025-07-17 01:46:31,836 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/zQY0aCQW39lN0Pg2uFTanCpwPLgZqnQwV5Dgu4jQP9fPJ2hqX0vIhFP7rhh7d1R5
2025-07-17 01:46:32,032 - INFO - [run] - WebSocket 连接成功。
2025-07-17 01:46:32,033 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['solusdc@bookTicker'], 'id': 1}
2025-07-17 01:46:32,150 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 168.5762, 新范围: [166.2366, 170.9157]
2025-07-17 01:46:32,151 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.2700
2025-07-17 01:46:32,151 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.2800
2025-07-17 01:46:32,151 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.3000
2025-07-17 01:46:32,152 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.3200
2025-07-17 01:46:32,152 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.3600
2025-07-17 01:46:32,153 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.4100
2025-07-17 01:46:32,153 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.4700
2025-07-17 01:46:32,153 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.5600
2025-07-17 01:46:32,154 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.6900
2025-07-17 01:46:32,155 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 166.8500
2025-07-17 01:46:32,155 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.0500
2025-07-17 01:46:32,155 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.3100
2025-07-17 01:46:32,156 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.6300
2025-07-17 01:46:32,157 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 167.9800
2025-07-17 01:46:32,157 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 168.3700
2025-07-17 01:46:32,158 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 168.7800
2025-07-17 01:46:32,159 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 169.1700
2025-07-17 01:46:32,159 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 169.5300
2025-07-17 01:46:32,160 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 169.8400
2025-07-17 01:46:32,162 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.1000
2025-07-17 01:46:32,162 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.3100
2025-07-17 01:46:32,163 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.4700
2025-07-17 01:46:32,163 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.5900
2025-07-17 01:46:32,163 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.6800
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.7500
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.7900
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8300
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8500
2025-07-17 01:46:32,164 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8700
2025-07-17 01:46:32,165 - INFO - [place_order] - 准备下单: buy LONG 0.1152 @ 170.8800
2025-07-17 01:46:32,379 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,380 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,381 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,381 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,381 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:46:32,382 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,616 - INFO - [execute_argm_strategy] - 网格重绘触发！新中枢: 168.5867, 新范围: [166.0605, 171.1128]
2025-07-17 01:47:32,680 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992160-de46
2025-07-17 01:47:32,732 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992152-964e
2025-07-17 01:47:32,733 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-672d
2025-07-17 01:47:32,733 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992154-a7e7
2025-07-17 01:47:32,735 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992155-40b4
2025-07-17 01:47:32,737 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992157-0c24
2025-07-17 01:47:32,737 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992155-1a22
2025-07-17 01:47:32,737 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992163-09ae
2025-07-17 01:47:32,738 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992155-7123
2025-07-17 01:47:32,738 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992157-bb96
2025-07-17 01:47:32,756 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992156-cc31
2025-07-17 01:47:32,757 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,757 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992159-6214
2025-07-17 01:47:32,758 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,758 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992151-f392
2025-07-17 01:47:32,759 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,760 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992162-1016
2025-07-17 01:47:32,760 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,760 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,760 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,761 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,762 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,764 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,764 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992153-d5c4
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992151-d104
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992153-fea7
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992151-60c3
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-c01c
2025-07-17 01:47:32,765 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992159-406e
2025-07-17 01:47:32,788 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-3669
2025-07-17 01:47:32,840 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992163-9f88
2025-07-17 01:47:32,841 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-fe2f
2025-07-17 01:47:32,843 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992163-2539
2025-07-17 01:47:32,843 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:32,843 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992162-c4c4
2025-07-17 01:47:32,844 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992164-dd2c
2025-07-17 01:47:32,844 - INFO - [cancel_order] - 订单已请求撤销: ClientID=x-argm-1752687992165-d0f1
2025-07-17 01:47:32,844 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.0900
2025-07-17 01:47:32,844 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1100
2025-07-17 01:47:32,844 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1300
2025-07-17 01:47:32,845 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1500
2025-07-17 01:47:32,845 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.1900
2025-07-17 01:47:32,846 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.2400
2025-07-17 01:47:32,846 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.5500
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.7200
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 166.9400
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 167.2200
2025-07-17 01:47:32,847 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 167.5600
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 167.9500
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 168.8000
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 169.2300
2025-07-17 01:47:32,848 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 169.6100
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 169.9500
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.2300
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.4500
2025-07-17 01:47:32,849 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.6300
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.7600
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.8600
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.9300
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 170.9800
2025-07-17 01:47:32,850 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0200
2025-07-17 01:47:32,851 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0500
2025-07-17 01:47:32,851 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0700
2025-07-17 01:47:32,851 - INFO - [place_order] - 准备下单: buy LONG 0.1149 @ 171.0800
2025-07-17 01:47:33,074 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,078 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,079 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,080 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,080 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,082 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,087 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,401 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,401 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,401 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,402 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,402 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,403 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,403 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,404 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,404 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,482 - WARNING - [_put_conn] - Connection pool is full, discarding connection: fapi.binance.com. Connection pool size: 10
2025-07-17 01:47:33,482 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992160-de46
2025-07-17 01:47:33,483 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992152-964e
2025-07-17 01:47:33,483 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-672d
2025-07-17 01:47:33,483 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992154-a7e7
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992155-40b4
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992157-0c24
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992155-7123
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992163-09ae
2025-07-17 01:47:33,484 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992155-1a22
2025-07-17 01:47:33,486 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992157-bb96
2025-07-17 01:47:33,486 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992156-cc31
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992159-6214
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992151-f392
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992151-d104
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992153-fea7
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992162-1016
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992153-d5c4
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992151-60c3
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-c01c
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992159-406e
2025-07-17 01:47:33,487 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-3669
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992163-9f88
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-fe2f
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992162-c4c4
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992164-dd2c
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992165-d0f1
2025-07-17 01:47:33,488 - INFO - [handle_order_update] - 订单终结 (CANCELED): ID: x-argm-1752687992163-2539
2025-07-17 01:48:10,720 - INFO - [close] - 正在关闭程序...
2025-07-17 01:48:10,837 - INFO - [cancel_all_open_orders] - 正在取消 30 个挂单...
2025-07-17 01:48:10,899 - INFO - [cancel_all_open_orders] - 所有挂单已取消
2025-07-17 01:48:10,899 - INFO - [close] - 程序已关闭。
2025-07-17 01:50:17,461 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:50:17,461 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:50:17,468 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:50:17,468 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:50:17,470 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:50:17,470 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:50:17,470 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:50:38,832 - INFO - [close] - 正在关闭程序...
2025-07-17 01:50:39,144 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688238895&recvWindow=5000&signature=d8ad93292eddc9efbe6491351e38eb15e2caec51886d1b532f0b417afc74e2c4
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 550, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688238895&recvWindow=5000&signature=d8ad93292eddc9efbe6491351e38eb15e2caec51886d1b532f0b417afc74e2c4
2025-07-17 01:50:39,397 - INFO - [close] - 程序已关闭。
2025-07-17 01:50:49,136 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:50:49,136 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:50:49,144 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:50:49,144 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:50:49,146 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:50:49,146 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:50:49,146 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:51:10,505 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:51:10,505 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:51:10,506 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 662, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:51:10,507 - INFO - [close] - 正在关闭程序...
2025-07-17 01:51:31,601 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688270568&recvWindow=5000&signature=262acecbcce907fa2c8447755793908945a13538567ed4bc8f815ad86c49b1f1
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('************', 443), [Errno 10051] Connect call failed ('2001::b93c:d832', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 550, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688270568&recvWindow=5000&signature=262acecbcce907fa2c8447755793908945a13538567ed4bc8f815ad86c49b1f1
2025-07-17 01:51:31,864 - INFO - [close] - 程序已关闭。
2025-07-17 01:51:31,864 - INFO - [main] - 程序已完全退出。
2025-07-17 01:52:25,909 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:52:25,916 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897)
2025-07-17 01:52:26,000 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 01:52:26,001 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:52:26,001 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:52:26,008 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:52:26,008 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:52:26,010 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:52:26,010 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:52:26,010 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:52:47,368 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:52:47,368 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:52:47,370 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 687, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:52:47,370 - INFO - [close] - 正在关闭程序...
2025-07-17 01:53:08,500 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752688367476&recvWindow=5000&signature=ca99a2e554df1f26398d4a5b19e14201722dc2f8da58c3219e617833eab79077
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10051] Connect call failed ('2001::9df0:1124', 443, 0, 0), [Errno 10060] Connect call failed ('***************', 443)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 550, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/margin/allPairs?timestamp=1752688367476&recvWindow=5000&signature=ca99a2e554df1f26398d4a5b19e14201722dc2f8da58c3219e617833eab79077
2025-07-17 01:53:08,761 - INFO - [close] - 程序已关闭。
2025-07-17 01:53:08,761 - INFO - [main] - 程序已完全退出。
2025-07-17 01:54:48,716 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:54:48,723 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897)
2025-07-17 01:54:48,803 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 01:54:48,804 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:54:48,804 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:54:48,812 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:54:48,812 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:54:48,813 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:54:48,814 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:54:48,814 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:54:49,381 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:54:49,381 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:54:49,382 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 687, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:54:49,382 - INFO - [close] - 正在关闭程序...
2025-07-17 01:55:24,162 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:55:24,168 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897)
2025-07-17 01:55:24,249 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 01:55:24,250 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:55:24,250 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:55:24,258 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:55:24,259 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:55:24,261 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:55:24,261 - INFO - [test_connection] - 正在测试API连接...
2025-07-17 01:55:24,261 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:55:45,614 - ERROR - [test_connection] - 连接测试失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:55:45,614 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:55:45,615 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 687, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 254, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 226, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:55:45,615 - INFO - [close] - 正在关闭程序...
2025-07-17 01:56:06,753 - WARNING - [__del__] - binanceusdm requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-07-17 01:56:06,754 - ERROR - [default_exception_handler] - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000023EFFF93C50>
2025-07-17 01:58:33,918 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 01:58:33,918 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 01:58:36,374 - INFO - [main] - 代理服务器连接正常
2025-07-17 01:58:36,379 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 01:58:36,471 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-17 01:58:36,472 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 01:58:36,472 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 01:58:36,479 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 01:58:36,479 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 01:58:36,481 - INFO - [setup] - 正在执行启动设置...
2025-07-17 01:58:36,481 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 01:58:36,481 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 01:58:57,832 - ERROR - [test_connection] - 连接测试失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 01:58:57,832 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 257, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:58:57,833 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 768, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 285, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 257, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 01:58:57,834 - INFO - [close] - 正在关闭程序...
2025-07-17 01:58:58,098 - INFO - [close] - 程序已关闭。
2025-07-17 01:58:58,098 - INFO - [main] - 程序已完全退出。
2025-07-17 02:00:38,730 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:00:38,730 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:00:41,389 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:00:41,394 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:00:41,474 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:00:41,475 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:00:41,476 - INFO - [_initialize_exchange] - 已设置代理: http://127.0.0.1:7897
2025-07-17 02:00:41,483 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:00:41,484 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:00:41,485 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:00:41,485 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:00:41,485 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:01:03,107 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:01:03,107 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:01:03,288 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:01:03,288 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:01:24,418 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688863353&recvWindow=10000&signature=3b2f638250a678d42da4ad0b1eaa9481a33ec9d6a1e212f352c2845a1b1f172b
2025-07-17 02:01:24,418 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:01:45,462 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:01:45,462 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:01:45,463 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:01:50,474 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:01:50,474 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:02:11,565 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:02:11,565 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:02:11,651 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:02:11,652 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:02:32,917 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752688931706&recvWindow=10000&signature=9f5303561104c9b91b7b06a093063e1bb3c1a0ca3f91f887663f74acfe1d4062
2025-07-17 02:02:32,917 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:02:53,976 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:02:53,977 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:02:53,977 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:02:58,988 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:02:58,988 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:03:20,070 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:03:20,070 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:03:20,157 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:03:20,157 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:03:41,262 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689000210&recvWindow=10000&signature=284b96cb4d97f64bde13e83dfb92a81cbbb16d48b1762b257831ecb1855281dd
2025-07-17 02:03:41,262 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:04:02,308 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:04:02,308 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:04:02,308 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:04:02,308 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 279, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:04:02,309 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 790, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 307, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 279, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:04:02,310 - INFO - [close] - 正在关闭程序...
2025-07-17 02:04:02,573 - INFO - [close] - 程序已关闭。
2025-07-17 02:04:02,573 - INFO - [main] - 程序已完全退出。
2025-07-17 02:04:29,675 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:04:29,676 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:04:30,212 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:04:30,217 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:04:30,300 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:04:30,301 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:04:30,301 - WARNING - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897，但由于CCXT库兼容性问题，暂时禁用代理
2025-07-17 02:04:30,301 - WARNING - [_initialize_exchange] - 如果您在中国大陆，请确保您的网络环境可以直接访问Binance API
2025-07-17 02:04:30,309 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:04:30,309 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:04:30,310 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:04:30,311 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:04:30,311 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:04:51,666 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:04:51,666 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:04:51,751 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:04:51,752 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:04:52,071 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689091806&recvWindow=10000&signature=d6bd83213dff8d98f638e6e453acc31ccfe8bfeee47e293f4ee80b717f82188c
2025-07-17 02:04:52,071 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:05:13,128 - INFO - [close] - 正在关闭程序...
2025-07-17 02:05:13,391 - INFO - [close] - 程序已关闭。
2025-07-17 02:06:55,555 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:06:55,555 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:06:57,013 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:06:57,018 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:06:57,096 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:06:57,096 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:06:57,096 - INFO - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:06:57,096 - INFO - [_initialize_exchange] - 已设置代理环境变量
2025-07-17 02:06:57,103 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:06:57,103 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:06:57,105 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:06:57,106 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:06:57,106 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:07:18,477 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:07:18,477 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:07:18,559 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:07:18,560 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:07:18,789 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689238614&recvWindow=10000&signature=167022a0976cd974e608a9e1632c3145eba49a3e6ef0ac65d82f76274a0029d1
2025-07-17 02:07:18,790 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:07:39,854 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:07:39,854 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:07:39,854 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:07:44,865 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:07:44,866 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:08:05,950 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:08:05,950 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:08:06,033 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:08:06,034 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:08:06,258 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689286091&recvWindow=10000&signature=99c5747eba6071004c552de0130c957699c3d06425de7c51c1142736e67c05c4
2025-07-17 02:08:06,258 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:08:27,304 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:08:27,304 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:08:27,305 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:08:32,311 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:08:32,311 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:08:53,383 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:08:53,383 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:08:53,464 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:08:53,465 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:09:14,544 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689333522&recvWindow=10000&signature=ab83eed03ecc7f8df3c3c56b046d12878da5159671d957a491d11fb0249137a3
2025-07-17 02:09:14,544 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:09:35,635 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:09:35,635 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:09:35,635 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:09:35,636 - ERROR - [setup] - 初始化设置失败: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 292, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:09:35,636 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: API连接测试失败
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 803, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 320, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 292, in setup
    if not await self.test_connection(): raise Exception("API连接测试失败")
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: API连接测试失败
2025-07-17 02:09:35,637 - INFO - [close] - 正在关闭程序...
2025-07-17 02:09:35,900 - INFO - [close] - 程序已关闭。
2025-07-17 02:09:35,900 - INFO - [main] - 程序已完全退出。
2025-07-17 02:10:16,379 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:10:16,379 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:10:17,847 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:10:17,852 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:10:17,937 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.09s
2025-07-17 02:10:17,937 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:10:17,937 - INFO - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:10:17,938 - INFO - [_initialize_exchange] - 已设置代理环境变量
2025-07-17 02:10:17,946 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:10:17,946 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:10:17,948 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:10:17,948 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:10:17,948 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:10:39,281 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:10:39,281 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:10:39,378 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:10:39,379 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:11:00,492 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689439437&recvWindow=10000&signature=bc8cf1993ab69661b7e8e8091ab21a91e7c46e6cef1db13265c5bf6f04074560
2025-07-17 02:11:00,492 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:11:21,565 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:11:21,565 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:11:21,565 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:11:26,569 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:11:26,569 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:11:47,640 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:11:47,640 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:11:47,720 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:11:47,720 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:11:47,931 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689507762&recvWindow=10000&signature=d58daac9525341ff3f379091e4858fb3f37df1a93025ed8f81e5c77c223db390
2025-07-17 02:11:47,932 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:12:08,962 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:12:08,962 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:12:08,963 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:12:13,971 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:12:13,971 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:12:35,056 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:12:35,056 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:12:35,138 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:12:35,139 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:12:35,365 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689555195&recvWindow=10000&signature=614dc4bc1e6160e73161ca14a16099cdaffa136b9aef5de1984dc90b0fe2d422
2025-07-17 02:12:35,365 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:12:56,398 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:12:56,399 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:12:56,399 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:12:56,399 - WARNING - [setup] - API连接测试失败，但继续尝试初始化...
2025-07-17 02:12:56,399 - INFO - [setup] - 正在加载市场数据...
2025-07-17 02:12:56,624 - ERROR - [setup] - 初始化设置失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 301, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 2813, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
2025-07-17 02:12:56,630 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1283, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs, sock=sock)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1148, in create_connection
    transport, protocol = await self._create_connection_transport(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\base_events.py", line 1181, in _create_connection_transport
    await waiter
  File "C:\Python312\Lib\asyncio\selector_events.py", line 988, in _read_ready__get_buffer
    nbytes = self._sock.recv_into(buf)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [远程主机强迫关闭了一个现有的连接。]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 809, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 326, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 301, in setup
    await self.exchange.load_markets(True)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 284, in load_markets_helper
    currencies = await self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 2813, in fetch_currencies
    results = await asyncio.gather(*promises)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689576445&recvWindow=10000&signature=92ca2854923229eb288e94be0a333bf078d706b6de2c18192c713f3c58fb18c9
2025-07-17 02:12:56,632 - INFO - [close] - 正在关闭程序...
2025-07-17 02:12:56,883 - INFO - [close] - 程序已关闭。
2025-07-17 02:12:56,883 - INFO - [main] - 程序已完全退出。
2025-07-17 02:13:27,626 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 02:13:27,627 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:13:29,267 - INFO - [main] - 代理服务器连接正常
2025-07-17 02:13:29,271 - INFO - [main] - 正在测试 ping 端点: https://fapi.binance.com/fapi/v1/ping (代理: http://127.0.0.1:7897) - 尝试 1/3
2025-07-17 02:13:29,351 - INFO - [main] - API 端点预检成功, 响应码: 200, 耗时: 0.08s
2025-07-17 02:13:29,352 - INFO - [main] - 启动 ARGM-V6.0 'Singularity' 策略...
2025-07-17 02:13:29,352 - INFO - [_initialize_exchange] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 02:13:29,352 - INFO - [_initialize_exchange] - 已设置代理环境变量
2025-07-17 02:13:29,359 - INFO - [_initialize_exchange] - 交易所实例创建成功
2025-07-17 02:13:29,359 - INFO - [__init__] - 已设置WebSocket URL: wss://fstream.binance.com
2025-07-17 02:13:29,360 - INFO - [setup] - 正在执行启动设置...
2025-07-17 02:13:29,360 - INFO - [test_connection] - 正在测试API连接... (尝试 1/3)
2025-07-17 02:13:29,360 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:13:50,701 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:13:50,701 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:13:50,786 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:13:50,787 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:14:11,882 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689630841&recvWindow=10000&signature=13ec3af029a6b4fc18dea2f0b5e1bf1e48f68738b612208dfef92a5050992fa8
2025-07-17 02:14:11,882 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:14:32,923 - ERROR - [test_connection] - 网络连接失败 (尝试 1/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:14:32,923 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:14:32,923 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:14:37,938 - INFO - [test_connection] - 正在测试API连接... (尝试 2/3)
2025-07-17 02:14:37,938 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:14:59,047 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:14:59,047 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:14:59,134 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:14:59,135 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:15:20,228 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689699189&recvWindow=10000&signature=79ccc055cb3eb9b1cd0f0395b3e7bdc29d809beae22823dd8de3680dd14cee81
2025-07-17 02:15:20,228 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:15:41,273 - ERROR - [test_connection] - 网络连接失败 (尝试 2/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:15:41,273 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:15:41,273 - INFO - [test_connection] - 等待 5 秒后重试...
2025-07-17 02:15:46,287 - INFO - [test_connection] - 正在测试API连接... (尝试 3/3)
2025-07-17 02:15:46,287 - INFO - [test_connection] - 正在检查Futures API连通性 (ping)...
2025-07-17 02:16:07,397 - ERROR - [test_connection] - Ping失败: binanceusdm GET https://fapi.binance.com/fapi/v1/ping
2025-07-17 02:16:07,397 - INFO - [test_connection] - 尝试使用基础HTTP请求测试连接...
2025-07-17 02:16:07,481 - INFO - [test_connection] - 基础HTTP ping成功
2025-07-17 02:16:07,482 - INFO - [test_connection] - 正在测试ticker数据获取...
2025-07-17 02:16:28,564 - ERROR - [test_connection] - Ticker获取失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689767520&recvWindow=10000&signature=2c0b228c4ca7971ac034510a444cd35db626c27f988b66849a51d5b250c8aae2
2025-07-17 02:16:28,564 - INFO - [test_connection] - 尝试获取服务器时间...
2025-07-17 02:16:49,591 - ERROR - [test_connection] - 网络连接失败 (尝试 3/3): binanceusdm GET https://fapi.binance.com/fapi/v1/time
2025-07-17 02:16:49,591 - ERROR - [test_connection] - 请检查代理服务器是否正常运行
2025-07-17 02:16:49,591 - ERROR - [test_connection] - 所有连接测试尝试均失败
2025-07-17 02:16:49,592 - WARNING - [setup] - API连接测试失败，但继续尝试初始化...
2025-07-17 02:16:49,592 - INFO - [setup] - 正在加载市场数据...
2025-07-17 02:17:10,665 - WARNING - [setup] - 加载市场数据失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689809638&recvWindow=10000&signature=a850d761e3a5a625f69a2da508f6e2c5be076a541916ab8a5253da2a7b1dacc1，使用默认市场配置
2025-07-17 02:17:31,742 - ERROR - [set_hedge_mode] - 设置双向持仓模式失败: binanceusdm POST https://fapi.binance.com/fapi/v1/positionSide/dual
2025-07-17 02:17:52,830 - ERROR - [set_leverage] - 设置杠杆失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689851789&recvWindow=10000&signature=a548cd343c91686f100b5a3cb44eb05f34b86e199a0dd9b79ba81e6ffa0c97c2
2025-07-17 02:18:13,891 - ERROR - [full_state_sync] - 状态完全同步失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689872846&recvWindow=10000&signature=a718122a8ffc282879bff2cf549ef9a6bd7661a96ed047a0d6f7417e6eff2da2
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('************', 443), [Errno 10051] Connect call failed ('2001::6ca0:a7a7', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host api.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 676, in full_state_sync
    positions, open_orders, ticker = await asyncio.gather(positions_f, orders_f, ticker_f)
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 4104, in fetch_ticker
    await self.load_markets()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 327, in load_markets
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 319, in load_markets
    result = await self.markets_loading
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.ExchangeNotAvailable: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689872846&recvWindow=10000&signature=a718122a8ffc282879bff2cf549ef9a6bd7661a96ed047a0d6f7417e6eff2da2
2025-07-17 02:18:34,937 - WARNING - [setup] - 获取价格失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689893896&recvWindow=10000&signature=c29d39f9898702932fdf3747675f6c6cd8831dbd18faa9e62005bc24d1bcaadc，使用默认价格
2025-07-17 02:18:55,976 - ERROR - [update_price_series] - 更新K线数据失败: binanceusdm GET https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752689914937&recvWindow=10000&signature=8dffe5c33be146973250e0d96c841e703b537ad9be0c7ddd1ce1fc4538ef2a5e
2025-07-17 02:18:55,976 - INFO - [fetch_listen_key] - 正在获取新的Listen Key...
2025-07-17 02:19:17,074 - ERROR - [fetch_listen_key] - 获取 listen key 失败: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::c710:9c0b', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 757, in fetch_listen_key
    response = await self.exchange.fapiPrivatePostListenKey()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
2025-07-17 02:19:17,076 - ERROR - [setup] - 初始化设置失败: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::c710:9c0b', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 346, in setup
    self.listen_key = await self.fetch_listen_key()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 757, in fetch_listen_key
    response = await self.exchange.fapiPrivatePostListenKey()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
2025-07-17 02:19:17,077 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohappyeyeballs\impl.py", line 149, in start_connection
    raise OSError(msg)
OSError: Multiple exceptions: [Errno 10060] Connect call failed ('*************', 443), [Errno 10051] Connect call failed ('2001::c710:9c0b', 443, 0, 0)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 208, in fetch
    async with session_method(yarl.URL(url, encoded=True),
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 1488, in __aenter__
    self._resp: _RetType = await self._coro
                           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1209, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientConnectorError: Cannot connect to host fapi.binance.com:443 ssl:default [None]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 841, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 358, in run
    await self.setup()
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 346, in setup
    self.listen_key = await self.fetch_listen_key()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 757, in fetch_listen_key
    response = await self.exchange.fapiPrivatePostListenKey()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\binance.py", line 11339, in request
    response = await self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 932, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 923, in fetch2
    return await self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\fuli_BN.py", line 166, in fetch
    return await super().fetch(url, method, headers, body)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\async_support\base\exchange.py", line 246, in fetch
    raise ExchangeNotAvailable(details) from e
ccxt.base.errors.ExchangeNotAvailable: binanceusdm POST https://fapi.binance.com/fapi/v1/listenKey
2025-07-17 02:19:17,079 - INFO - [close] - 正在关闭程序...
2025-07-17 02:19:27,094 - WARNING - [close] - 关闭时取消挂单失败，但程序将继续关闭: 
2025-07-17 02:19:27,357 - INFO - [close] - 程序已关闭。
2025-07-17 02:19:27,357 - INFO - [main] - 程序已完全退出。
2025-07-17 22:32:15,827 - INFO - [main] - 启动前预检 API 连接...
2025-07-17 22:32:15,827 - INFO - [main] - 检测到代理配置: http://127.0.0.1:7897
2025-07-17 22:32:26,312 - ERROR - [main] - 代理服务器连接失败: 
2025-07-17 22:32:26,313 - ERROR - [main] - 请检查代理服务器是否正在运行且配置正确
