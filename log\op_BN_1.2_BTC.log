2025-07-15 00:39:58,027 - INFO - [main] - 启动 Delta趋势偏斜网格策略 V4.2 - [BTC]
2025-07-15 00:40:19,437 - INFO - [main] - 启动 Delta趋势偏斜网格策略 V4.2 - [BTC]
2025-07-15 00:40:19,444 - INFO - [_initialize_exchange] - 交易所实例创建成功，代理: 启用
2025-07-15 00:40:19,445 - INFO - [setup] - 正在执行启动设置...
2025-07-15 00:40:19,544 - ERROR - [setup] - 启动设置失败: binanceusdm {"code":-1022,"msg":"Signature for this request is not valid."}
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 582, in fetch
    response.raise_for_status()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error:  for url: https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752511219445&recvWindow=10000&signature=3deb04adf605fb1450f2be68028f1645bb66b889478e3d11aba8f887d6a05499

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\op_BN_1.2.py", line 138, in setup
    await run_sync(self.exchange.load_markets, True); self.market = self.exchange.market(self.symbol)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 1551, in load_markets
    currencies = self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\binance.py", line 2808, in fetch_currencies
    promises = [self.sapiGetCapitalConfigGetall(params)]
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\binance.py", line 11338, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4527, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4516, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\op_BN_1.2.py", line 127, in fetch
    headers['User-Agent'] = 'Mozilla/5.0'; return super().fetch(url, method, headers, body)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 598, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\binance.py", line 11306, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], error, feedback)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4920, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.AuthenticationError: binanceusdm {"code":-1022,"msg":"Signature for this request is not valid."}
2025-07-15 00:40:19,551 - CRITICAL - [main] - 程序顶层发生未捕获的严重错误: binanceusdm {"code":-1022,"msg":"Signature for this request is not valid."}
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 582, in fetch
    response.raise_for_status()
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 400 Client Error:  for url: https://api.binance.com/sapi/v1/capital/config/getall?timestamp=1752511219445&recvWindow=10000&signature=3deb04adf605fb1450f2be68028f1645bb66b889478e3d11aba8f887d6a05499

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\grid_trate\op_BN_1.2.py", line 506, in main
    await bot.run()
  File "c:\Users\<USER>\Desktop\grid_trate\op_BN_1.2.py", line 150, in run
    await self.setup(); await self.cancel_all_open_orders()
    ^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\op_BN_1.2.py", line 138, in setup
    await run_sync(self.exchange.load_markets, True); self.market = self.exchange.market(self.symbol)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 1551, in load_markets
    currencies = self.fetch_currencies()
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\binance.py", line 2808, in fetch_currencies
    promises = [self.sapiGetCapitalConfigGetall(params)]
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\binance.py", line 11338, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4527, in fetch2
    raise e
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4516, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\grid_trate\op_BN_1.2.py", line 127, in fetch
    headers['User-Agent'] = 'Mozilla/5.0'; return super().fetch(url, method, headers, body)
                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 598, in fetch
    skip_further_error_handling = self.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)
                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\binance.py", line 11306, in handle_errors
    self.throw_exactly_matched_exception(self.exceptions['exact'], error, feedback)
  File "C:\Users\<USER>\.virtualenvs\venv1-KwQRlGVv\Lib\site-packages\ccxt\base\exchange.py", line 4920, in throw_exactly_matched_exception
    raise exact[string](message)
ccxt.base.errors.AuthenticationError: binanceusdm {"code":-1022,"msg":"Signature for this request is not valid."}
2025-07-15 00:40:19,552 - INFO - [close] - 正在关闭程序...
2025-07-15 00:40:19,625 - ERROR - [cancel_all_open_orders] - 批量撤销所有订单失败: binanceusdm {"code":-1022,"msg":"Signature for this request is not valid."}
2025-07-15 00:40:19,625 - INFO - [close] - 所有挂单已取消。
2025-07-15 00:40:19,625 - INFO - [close] - 程序已关闭。
2025-07-15 00:40:19,625 - INFO - [main] - 程序已完全退出。
