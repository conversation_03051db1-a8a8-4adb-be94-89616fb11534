2025-07-15 00:17:58,957 - INFO - [main] - 启动 Delta趋势偏斜网格策略 V4.0 - [BTC]
2025-07-15 00:17:58,957 - ERROR - [_initialize_exchange] - API密钥未配置，请在CONFIG中设置后再运行。
2025-07-15 00:18:22,397 - INFO - [main] - 启动 Delta趋势偏斜网格策略 V4.0 - [BTC]
2025-07-15 00:18:22,403 - INFO - [_initialize_exchange] - 交易所实例创建成功，代理: 启用
2025-07-15 00:18:22,403 - INFO - [setup] - 正在执行启动设置...
2025-07-15 00:18:22,962 - INFO - [setup] - 成功加载市场数据, 合约精度: {'amount': 0.001, 'price': 0.1, 'cost': None, 'base': 1e-08, 'quote': 1e-08}
2025-07-15 00:18:23,051 - ERROR - [set_hedge_mode] - 设置双向持仓模式失败: binanceusdm {"code":-4059,"msg":"No need to change position side."}
2025-07-15 00:18:23,135 - INFO - [set_leverage] - 杠杆已设置为 10x
2025-07-15 00:18:23,362 - INFO - [run_strategy_cycle] - ==================== 开始新一轮策略决策 ====================
2025-07-15 00:18:44,530 - ERROR - [_fetch_and_set_delta_range] - 获取Deribit Delta区间失败: Cannot connect to host www.deribit.com:443 ssl:default [None]. 将使用上次的有效区间。
2025-07-15 00:18:44,597 - INFO - [run_strategy_cycle] - 决策更新完成。趋势: SIDEWAYS, 区间: [0.00-0.00], ATR: 914.16
2025-07-15 00:18:44,597 - INFO - [run_strategy_cycle] - ==================== 策略决策结束 ====================
2025-07-15 00:18:44,662 - INFO - [fetch_listen_key] - 成功获取新的 listen key。
2025-07-15 00:18:44,663 - INFO - [setup] - 初始化设置完成。
2025-07-15 00:18:44,663 - INFO - [setup] - 当前持仓: 多头=0.0, 均价=0.0
2025-07-15 00:18:44,663 - INFO - [setup] - 当前趋势: SIDEWAYS, 网格区间: [0.00 - 0.00]
2025-07-15 00:18:44,730 - INFO - [cancel_all_open_orders] - 所有挂单已成功请求取消。
2025-07-15 00:18:44,730 - INFO - [run] - 所有旧挂单已取消，准备根据新策略部署。
2025-07-15 00:18:44,730 - INFO - [run] - 正在连接WebSocket: wss://fstream.binance.com/ws/...
2025-07-15 00:18:44,937 - INFO - [run] - WebSocket 连接成功。
2025-07-15 00:18:44,938 - INFO - [run] - 已发送Ticker订阅请求: {'method': 'SUBSCRIBE', 'params': ['btcusdt@bookTicker'], 'id': 1}
2025-07-15 00:18:44,938 - INFO - [generate_target_grid] - 当前趋势为 SIDEWAYS，非上升趋势，不部署网格。
2025-07-15 00:18:44,938 - INFO - [deploy_grid] - 未生成目标订单，可能因趋势不明朗或持仓已满。将撤销所有挂单。
2025-07-15 00:18:45,003 - INFO - [cancel_all_open_orders] - 所有挂单已成功请求取消。
2025-07-15 00:23:59,207 - INFO - [close] - 正在关闭程序...
2025-07-15 00:23:59,273 - INFO - [cancel_all_open_orders] - 所有挂单已成功请求取消。
2025-07-15 00:23:59,273 - INFO - [close] - 所有挂单已取消。
2025-07-15 00:23:59,273 - INFO - [close] - 程序已关闭。
