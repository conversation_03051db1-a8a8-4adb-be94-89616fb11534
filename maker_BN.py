# ------------------------------------------------------------------------------------
# 自适应库存管理做市策略 V1.0
# ------------------------------------------------------------------------------------
# 作者: Lens
# 日期: 2025-07-10
# 描述: 本脚本实现了一个基于主动库存管理、动态价差(ATR)和公允价值(EMA)的高级做市策略。
#              它会根据净库存水平自动在三种操作模式之间切换。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import ccxt.async_support as ccxt  # 使用异步版本的ccxt
import math
import os
import sys
import uuid
import pandas as pd

# ====================================================================================
# --- 自定义交易所类 ---
# ====================================================================================
class CustomGate(ccxt.binance):
    """自定义交易所类，用于在需要时添加特定的请求头。"""
    def fetch(self, url, method='GET', headers=None, body=None):
        if headers is None:
            headers = {}
        # headers['X-Gate-Channel-Id'] = 'laohuoji' # 示例：可以取消注释以添加自定义头
        # headers['Accept'] = 'application/json'
        # headers['Content-Type'] = 'application/json'
        return super().fetch(url, method, headers, body)

# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "vfP78BkDu0lec7juXjS6vqt9HnXPYwkj6EQmWM1N61j92gc1Y5rjWFmN14DTVdn9",      # ❗❗❗ 替换为你的 API Key
    "api_secret": "F4n5LfEsXczeNpNOMpU2poQdd6WRPKAW0XhxRsn1JhvtUapA0OwpffisciKkl3v5",  # ❗❗❗ 替换为你的 API Secret
    "use_proxy": True,                 # 是否使用代理 (True/False)
    "proxy_url": "http://127.0.0.1:7897", # 代理服务器地址

    # --- 策略核心参数 ---
    "coin_name": "ETH",                 # 交易币种
    "contract_type": "USDC",            # 合约类型: USDC 或 USDT
    "leverage": 20,                     # 杠杆倍数
    "initial_value": 100,               # ✅ 单笔订单的基础名义价值 (USDC)

    # --- 报价与网格逻辑 ---
    "grid": {
        "dynamic_spacing_enabled": True,       # 是否启用基于ATR的动态间距
        "atr_period": 14,                      # ATR计算周期 (K线数量)
        "atr_timeframe": '5m',                 # ATR计算使用的K线周期
        "atr_multiplier": 0.6,                 # ✅ ATR波动率乘数，越大价差越宽，越安全
        "base_spacing_pct": 0.0015,            # 如果禁用动态间距，使用的固定间距 (0.15%)
        "fair_price_ema_period": 20            # 计算公允价值的EMA周期
    },

    # --- 风险与库存管理 ---
    "risk_management": {
        # ✅ 净头寸合约数量的硬性上限 (多-空)，这是最重要的风控参数
        "position_limit_contracts": 0.5,
        # ✅ 库存偏斜因子，越大，库存对报价影响越大，回归越快但可能错过成交
        "inventory_skew_factor": 0.001,
        # ✅ 订单调整阈值，价格偏离达到价差的多少比例时调整订单 (0.25 = 25%)
        "rebalance_threshold_pct": 0.25,
        # ✅ 防御模式触发阈值，净库存达到硬性上限的百分之多少时进入
        "defense_mode_trigger_pct": 0.5,
        # ✅ 安全补仓单距离系数，在防御模式下，补仓单挂在几倍ATR价差之外
        "defensive_add_spacing_multiplier": 2.0,
    },

    # --- 系统运行参数 ---
    "system": {
        "sync_interval_seconds": 60,           # REST API状态完全同步的间隔
        "websocket_url": "wss://fstream.binance.com/ws", # WebSocket URL
        "timeout_seconds": 30,                 # API请求超时时间
        "ticker_update_interval_ms": 200,      # Ticker更新处理的最小间隔(毫秒)，防止过于频繁地调整订单
        "max_connection_retries": 5,           # 最大连接重试次数
        "connection_retry_delay": 5,           # 连接重试延迟（秒）
        "heartbeat_interval": 30,              # WebSocket心跳间隔（秒）
        "state_sync_on_error": True,           # 发生错误时是否强制进行状态同步
        "emergency_sync_interval": 300         # 紧急状态同步间隔（秒），用于处理极端情况
    }
}


# ====================================================================================
# --- 日志配置 ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class AdaptiveMarketMakerBot:
    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()  # 异步锁，用于保护共享状态的并发访问

        # --- 交易所与市场信息 ---
        self.exchange = self._initialize_exchange()
        self.symbol = f"{config['coin_name']}/{config['contract_type']}:{config['contract_type']}"
        self.market = None

        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0

        # --- 核心策略状态 (单一事实来源) ---
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {}  # key: clientOrderId, value: 订单对象

        # --- 动态策略参数 ---
        self.base_quantity = 0.0       # 基础下单量 (根据initial_value和价格计算)
        self.grid_spacing_pct = 0.0    # 当前网格价差百分比 (可能基于ATR动态变化)
        self.fair_price = 0.0          # 公允价值 (通常基于EMA计算)

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_ticker_time = 0
        self.is_shutting_down = False

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if self.config['api_key'] == "YOUR_API_KEY" or self.config['api_secret'] == "YOUR_API_SECRET":
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        options = {
            "defaultType": "future", 
            "timeout": self.config['system']['timeout_seconds'] * 1000,
        }
        
        # 代理配置
        proxies = None
        if self.config['use_proxy']:
            proxies = {
                'http': self.config['proxy_url'], 
                'https': self.config['proxy_url']
            }
            logger.info(f"已设置代理: {self.config['proxy_url']}")

        exchange = CustomGate({
            "apiKey": self.config['api_key'],
            "secret": self.config['api_secret'],
            "options": options,
            "proxies": proxies,
        })
        
        logger.info("交易所实例创建成功")
        return exchange
    
    async def test_connection(self):
        """测试网络连接和API可用性"""
        try:
            logger.info("正在测试网络连接...")
            # 简单测试连接，获取交易所状态
            status = await self.exchange.fetch_status()
            
            if status and status.get('status') == 'ok':
                logger.info("网络连接测试成功")
                return True
            else:
                logger.error(f"API状态异常: {status}")
                return False
                
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    # --------------------------------------------------------------------------
    # --- 启动与生命周期管理 ---
    # --------------------------------------------------------------------------
    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        
        # 异步加载市场数据
        try:
            logger.info("正在连接交易所API并加载市场数据...")
            await self.exchange.load_markets(reload=False)
            logger.info("交易所API连接成功")
            
            # 验证市场数据是否正确加载
            if not self.exchange.markets or len(self.exchange.markets) == 0:
                raise Exception("Markets数据为空，加载失败")
            
            # 验证目标交易对是否存在
            if self.symbol not in self.exchange.markets:
                raise Exception(f"交易对 {self.symbol} 不存在于市场数据中")
                
            logger.info(f"成功加载 {len(self.exchange.markets)} 个交易对")
            
        except Exception as e:
            logger.error(f"连接交易所失败: {e}")
            logger.info("请检查网络连接或考虑使用代理服务器。如需使用代理，请设置use_proxy=true并配置proxy_url")
            raise
        
        # 获取market对象，方便后续使用
        self.market = self.exchange.market(self.symbol)
        
        await self.set_hedge_mode() # 设置为对冲模式
        await self.set_leverage() # 设置杠杆
        await self.full_state_sync() # 首次必须完全同步状态，确保本地状态与交易所一致
        
        if self.last_price == 0: # 如果同步后依然没有价格，再主动获取一次ticker
            ticker = await self.exchange.fetch_ticker(self.symbol)
            self.last_price = ticker['last']

        await self.update_dynamic_parameters() # 更新动态参数，如ATR、公允价等
        self.listen_key = await self.fetch_listen_key() # 获取WebSocket订阅所需的listenKey
        
        asyncio.create_task(self.keep_listen_key_alive()) # 启动一个任务来定期延长listenKey的有效期
        logger.info("初始化设置完成。")
        logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}, 净库存={self.net_inventory:.4f}")
        logger.info(f"初始下单量={self.base_quantity}, 初始网格价差={self.grid_spacing_pct:.4%}")

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        await self.setup()
        await self.cancel_all_open_orders()  # 启动时清理旧订单，确保状态干净

        max_retries = self.config['system']['max_connection_retries']
        retry_delay = self.config['system']['connection_retry_delay']
        
        while not self.is_shutting_down:
            for attempt in range(max_retries):
                try:
                    symbol_lower = self.market['id'].lower()
                    stream_url = f"{self.config['system']['websocket_url']}/stream?streams={symbol_lower}@bookTicker/{self.listen_key}"
                    
                    logger.info(f"正在连接WebSocket (尝试 {attempt + 1}/{max_retries}): {stream_url}")
                    
                    # 设置连接超时和心跳，以保持连接稳定
                    async with websockets.connect(
                        stream_url,
                        ping_interval=20,
                        ping_timeout=10,
                        close_timeout=10
                    ) as websocket:
                        logger.info("WebSocket 连接成功，已订阅数据流。")
                        
                        while not self.is_shutting_down:
                            try:
                                message = await asyncio.wait_for(websocket.recv(), timeout=60)
                                data = json.loads(message)['data']
                                await self.handle_websocket_message(data)
                            except asyncio.TimeoutError:
                                logger.warning("WebSocket接收消息超时，检查连接状态...")
                                # 发送ping测试连接是否仍然存活
                                await websocket.ping()
                                continue
                            except websockets.exceptions.ConnectionClosed:
                                logger.warning("WebSocket连接已关閉，尝试重新连接...")
                                break
                    
                    # 如果正常退出内层循环，跳出重试循环
                    break
                    
                except (websockets.exceptions.InvalidURI, 
                       websockets.exceptions.InvalidHandshake,
                       OSError, 
                       websockets.exceptions.ConnectionClosed) as e:
                    if self.is_shutting_down: 
                        break
                    
                    logger.error(f"WebSocket连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                    
                    if attempt == max_retries - 1:
                        logger.error("WebSocket连接失败次数过多，等待更长时间后重试...")
                        await asyncio.sleep(30)
                        break
                    
                    # 指数退避重试机制，避免频繁失败
                    wait_time = retry_delay * (2 ** attempt)
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    
                    # 重新获取ListenKey，因为旧的可能已失效
                    try:
                        self.listen_key = await self.fetch_listen_key()
                        logger.info("已重新获取ListenKey")
                    except Exception as e:
                        logger.error(f"重新获取ListenKey失败: {e}")
                        continue
                        
                except Exception as e:
                    if self.is_shutting_down: 
                        break
                    logger.critical(f"主循环发生严重错误: {e}", exc_info=True)
                    await asyncio.sleep(15)
                    break
            
            if self.is_shutting_down:
                break
    
    async def close(self):
        """优雅关闭程序，取消所有挂单并关闭连接"""
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        await self.cancel_all_open_orders()
        if self.exchange:
            await self.exchange.close()
            logger.info("交易所连接已关闭。")

    # --------------------------------------------------------------------------
    # --- WebSocket 消息处理 ---
    # --------------------------------------------------------------------------
    async def handle_websocket_message(self, data):
        """处理来自WebSocket的消息，分发到不同的处理器"""
        event_type = data.get("e")
        if event_type == "bookTicker":
            current_time_ms = time.time() * 1000
            # 节流操作，避免过于频繁地处理ticker更新
            if current_time_ms - self.last_ticker_time < self.config['system']['ticker_update_interval_ms']:
                return
            self.last_ticker_time = current_time_ms
            await self.handle_ticker_update(data)

        elif event_type == "ORDER_TRADE_UPDATE":
            await self.handle_order_update(data)
            
        elif event_type == "listenKeyExpired":
            logger.warning("ListenKey 已过期，需要重连 WebSocket。")
            self.listen_key = await self.fetch_listen_key()
            # 抛出异常让主循环捕捉并进行重连
            raise websockets.exceptions.ConnectionClosed(1000, "ListenKey expired, must reconnect.")

    async def handle_ticker_update(self, data):
        """处理行情更新并触发策略调整"""
        self.best_bid_price = float(data['b'])
        self.best_ask_price = float(data['a'])
        new_last_price = (self.best_bid_price + self.best_ask_price) / 2
        
        # 仅在价格有显著变化时更新，避免过度计算
        if abs(new_last_price - self.last_price) / self.last_price > 0.0001:
            self.last_price = new_last_price
            await self.update_dynamic_parameters() # 价格变化时，重新计算ATR、公允价等

        # 定期完全同步状态，以防websocket消息丢失导致状态不一致
        if time.time() - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
            await self.full_state_sync()

        await self.adjust_grid_strategy() # 根据最新状态调整订单

    async def handle_order_update(self, data):
        """处理订单更新消息，维护本地订单和持仓状态"""
        order_data = data.get("o", {})
        if order_data.get("s") != self.market['id']:
            return # 忽略不相关的交易对
            
        async with self.lock:
            client_order_id = order_data['c']
            status = order_data['X']

            if status == "NEW":
                self.open_orders[client_order_id] = order_data
                logger.info(f"新订单: {order_data['S']} {order_data['ps']} {order_data['q']} @ {order_data['p']}, ID: {client_order_id}")
            
            elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                if client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]
                    logger.info(f"订单终结 ({status}): ID: {client_order_id}")

            elif status in ["FILLED", "PARTIALLY_FILLED"] and float(order_data['l']) > 0:
                filled_qty = float(order_data['l'])
                position_side = order_data['ps']
                side = order_data['S']

                # 更新多空头寸
                if position_side == 'LONG':
                    self.long_position_size += filled_qty if side == 'BUY' else -filled_qty
                elif position_side == 'SHORT':
                    self.short_position_size += filled_qty if side == 'SELL' else -filled_qty
                
                # 重新计算净库存
                self.net_inventory = self.long_position_size - self.short_position_size
                logger.info(f"✅ 订单成交: {side} {position_side} {filled_qty}. 最新持仓: 多={self.long_position_size:.4f}, 空={self.short_position_size:.4f}, 净库存={self.net_inventory:.4f}")
                
                if status == "FILLED" and client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]
    
    # --------------------------------------------------------------------------
    # --- 核心策略逻辑 ---
    # --------------------------------------------------------------------------
    async def adjust_grid_strategy(self):
        """自适应库存管理模型：根据净库存切换策略模式"""
        async with self.lock:
            net_inventory = self.net_inventory
            hard_limit = self.config['risk_management']['position_limit_contracts']
            defense_trigger = hard_limit * self.config['risk_management']['defense_mode_trigger_pct']
            single_trade_qty = self.base_quantity

            # --- 模式切换逻辑 ---
            if abs(net_inventory) >= hard_limit:
                logger.warning(f"模式: 紧急处理! 净库存 {net_inventory:.4f} 已达到硬性上限 {hard_limit}！")
                await self.execute_emergency_exit_logic()
            
            elif abs(net_inventory) >= defense_trigger:
                logger.info(f"模式: 防御网格. 净库存: {net_inventory:.4f}")
                await self.execute_defensive_grid_logic()

            elif abs(net_inventory) >= single_trade_qty * 0.5:
                logger.info(f"模式: 标准偏斜. 净库存: {net_inventory:.4f}")
                await self.execute_standard_skew_logic()
            
            else:
                logger.info(f"模式: 中性寻源. 净库存: {net_inventory:.4f}")
                await self.execute_neutral_mode_logic()

    async def execute_neutral_mode_logic(self):
        """模式一：中性寻源。目标是快速建立初始库存或在库存接近于零时进行交易。"""
        # 采用极小的偏斜，挂出紧凑的双边订单以增加成交概率
        skew_factor = self.config['risk_management']['inventory_skew_factor'] * 0.1
        skewed_mid_price = self.fair_price * (1 - self.net_inventory * skew_factor)
        
        target_bid = skewed_mid_price * (1 - self.grid_spacing_pct)
        target_ask = skewed_mid_price * (1 + self.grid_spacing_pct)
        
        target_orders = [
            {'side': 'buy', 'price': target_bid, 'positionSide': 'LONG', 'type': 'NEUTRAL_BID'},
            {'side': 'sell', 'price': target_ask, 'positionSide': 'SHORT', 'type': 'NEUTRAL_ASK'}
        ]
        await self.rebalance_orders(target_orders)

    async def execute_standard_skew_logic(self):
        """模式二：标准偏斜。核心做市模式，通过库存偏斜来管理风险并赚取价差。"""
        skew_factor = self.config['risk_management']['inventory_skew_factor']
        # 核心公式：库存偏离中性水平越多，报价就越远离公允价值，以激励反向交易，使库存回归。
        skewed_mid_price = self.fair_price * (1 - self.net_inventory * skew_factor)
        
        target_bid = skewed_mid_price * (1 - self.grid_spacing_pct)
        target_ask = skewed_mid_price * (1 + self.grid_spacing_pct)
        
        target_orders = [
            {'side': 'buy', 'price': target_bid, 'positionSide': 'LONG', 'type': 'SKEW_BID'},
            {'side': 'sell', 'price': target_ask, 'positionSide': 'SHORT', 'type': 'SKEW_ASK'}
        ]
        await self.rebalance_orders(target_orders)

    async def execute_defensive_grid_logic(self):
        """模式三：防御网格。当库存达到较高水平时，停止逆向开仓，专注于安全地降低库存。"""
        target_orders = []
        spacing_val = self.fair_price * self.grid_spacing_pct
        
        if self.net_inventory > 0:  # 多头库存过高
            # 1. 停止挂新的买单 (逆向操作)
            # 2. 在当前买一价之上设置一个止盈卖单，以平掉部分多头仓位
            take_profit_price = self.best_bid_price + spacing_val
            target_orders.append({'side': 'sell', 'price': take_profit_price, 'positionSide': 'LONG', 'type': 'DEFENSIVE_TP_L'})
            
            # 3. 在远低于当前价格的位置设置一个安全补仓买单，用于在价格大幅回调时降低持仓成本
            multiplier = self.config['risk_management']['defensive_add_spacing_multiplier']
            add_price = self.fair_price - (spacing_val * multiplier)
            target_orders.append({'side': 'buy', 'price': add_price, 'positionSide': 'LONG', 'type': 'DEFENSIVE_ADD_L'})
            
        elif self.net_inventory < 0: # 空头库存过高
            # 1. 停止挂新的卖单 (逆向操作)
            # 2. 在当前卖一价之下设置一个止盈买单，以平掉部分空头仓位
            take_profit_price = self.best_ask_price - spacing_val
            target_orders.append({'side': 'buy', 'price': take_profit_price, 'positionSide': 'SHORT', 'type': 'DEFENSIVE_TP_S'})
            
            # 3. 在远高于当前价格的位置设置一个安全补仓卖单
            multiplier = self.config['risk_management']['defensive_add_spacing_multiplier']
            add_price = self.fair_price + (spacing_val * multiplier)
            target_orders.append({'side': 'sell', 'price': add_price, 'positionSide': 'SHORT', 'type': 'DEFENSIVE_ADD_S'})
            
        await self.rebalance_orders(target_orders)

    async def execute_emergency_exit_logic(self):
        """紧急处理模式：当净库存触及硬性上限时，不再进行任何开仓，只允许平仓。"""
        target_orders = []
        if self.net_inventory > 0: # 只允许平多
            target_orders.append({'side': 'sell', 'price': self.best_bid_price, 'positionSide': 'LONG', 'type': 'EMERGENCY_EXIT_L'})
        elif self.net_inventory < 0: # 只允许平空
            target_orders.append({'side': 'buy', 'price': self.best_ask_price, 'positionSide': 'SHORT', 'type': 'EMERGENCY_EXIT_S'})
        
        # 在紧急情况下，平仓数量应为全部净库存，以尽快降低风险
        await self.rebalance_orders(target_orders, quantity_override=abs(self.net_inventory))


    # --------------------------------------------------------------------------
    # --- 订单与状态管理 ---
    # --------------------------------------------------------------------------
    async def rebalance_orders(self, target_orders, quantity_override=None):
        """订单协调核心逻辑：对比目标订单与现有订单，执行新增、修改、删除操作，确保场上订单符合当前策略模式。"""
        # 在操作前，先与交易所同步一次本地的订单状态，减少状态不一致的风险
        await self.sync_order_status()
        
        current_orders_copy = list(self.open_orders.values())
        rebalance_threshold = self.grid_spacing_pct * self.config['risk_management']['rebalance_threshold_pct']
        
        # 步骤一：遍历所有当前挂单，决定它们的去留
        for order in current_orders_copy:
            client_order_id = order.get('c')
            if not client_order_id: continue

            order_side = order['S'].lower()
            order_ps = order['ps']
            order_price = float(order['p'])

            match_found = False
            # 在目标订单列表中寻找是否有与之匹配的（方向相同）
            for target in list(target_orders): # 使用副本进行迭代，因为我们会修改原始列表
                if target['side'] == order_side and target['positionSide'] == order_ps:
                    # 找到了同方向的目标订单，现在判断价格是否需要调整
                    price_diff_pct = abs(order_price - target['price']) / target['price']
                    
                    if price_diff_pct > rebalance_threshold:
                        # 价格偏差超过阈值，说明需要重新报价，先取消旧订单
                        logger.info(f"订单价格需调整: {order_side} {order_ps}, 旧价:{order_price}, 新价:{target['price']:.4f}. 执行撤单...")
                        await self.cancel_order(client_order_id)
                    else:
                        # 价格在容忍范围内，保留此订单，并将其视为已处理
                        match_found = True
                    
                    # 不论是否撤单，这个目标已经被一个现有订单“认领”了，从目标列表中移除
                    target_orders.remove(target) 
                    break

            if not match_found:
                # 如果一个现有订单在目标列表中找不到任何匹配，说明它已不符合当前策略，应被取消
                logger.info(f"取消多余或过时的订单: {order_side} {order_ps} @ {order_price}")
                await self.cancel_order(client_order_id)

        # 步骤二：创建所有剩余在目标列表中的订单
        for target in target_orders:
            quantity = quantity_override if quantity_override is not None else self.base_quantity
            logger.info(f"创建新的策略订单: {target['side']} {target['positionSide']} @ {target['price']:.4f}")
            await self.place_order(target['side'], target['positionSide'], quantity, target['price'])
    
    async def sync_order_status(self):
        """通过REST API同步本地订单记录与交易所的实际挂单状态，确保数据一致性。"""
        max_retries = 2
        for attempt in range(max_retries):
            try:
                # 从交易所获取所有当前挂单
                exchange_orders = await asyncio.wait_for(
                    self.exchange.fetch_open_orders(self.symbol),
                    timeout=15
                )
                exchange_client_order_ids = {order.get('clientOrderId') for order in exchange_orders if order.get('clientOrderId')}
                
                # 本地记录的挂单ID集合
                local_client_order_ids = set(self.open_orders.keys())
                
                # 清理：如果一个订单在本地记录中，但交易所返回的列表中没有，说明它已被成交或取消
                for client_order_id in local_client_order_ids - exchange_client_order_ids:
                    logger.info(f"同步：移除已完成/撤销的订单: {client_order_id}")
                    del self.open_orders[client_order_id]
                
                # 补全：如果一个订单在交易所列表中，但本地没有记录，说明可能是通过其他渠道下单或状态同步中断导致
                for order in exchange_orders:
                    client_order_id = order.get('clientOrderId')
                    if client_order_id and client_order_id not in self.open_orders:
                        self.open_orders[client_order_id] = order.get('info', {})
                        logger.info(f"同步：添加遗漏的订单记录: {client_order_id}")
                
                logger.debug(f"订单状态同步完成，当前开放订单数: {len(self.open_orders)}")
                return
                
            except (asyncio.TimeoutError, ccxt.RequestTimeout, ccxt.NetworkError) as e:
                logger.warning(f"订单状态同步超时/网络错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    logger.error("订单状态同步失败，将暂时使用本地缓存的订单状态。")
                    return
                await asyncio.sleep(3)
            except Exception as e:
                logger.error(f"订单状态同步时发生未知错误: {e}")
                return

    async def place_order(self, side, position_side, amount, price):
        """封装统一的下单请求，自动处理开仓/平仓逻辑。"""
        if amount <= self.market['limits']['amount']['min']:
            logger.warning(f"下单数量 {amount} 小于最小允许值 {self.market['limits']['amount']['min']}，跳过本次下单。")
            return
        
        # 自动判断是否为平仓单 (reduce-only)
        is_reduce_only = False
        if (position_side == 'LONG' and side == 'sell' and self.long_position_size > 0) or \
           (position_side == 'SHORT' and side == 'buy' and self.short_position_size > 0):
            is_reduce_only = True
            # 平仓时，确保下单数量不超过当前持仓量
            if position_side == 'LONG':
                amount = min(amount, self.long_position_size)
            else:
                amount = min(amount, self.short_position_size)

        try:
            # 使用UUID确保client_order_id的唯一性，便于追踪
            client_order_id = f"x-amm-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
            params = {'positionSide': position_side.upper(), 'newClientOrderId': client_order_id, 'reduceOnly': is_reduce_only}
            
            await self.exchange.create_order(self.symbol, 'limit', side, amount, price, params)
            logger.info(f"订单已提交: {side} {position_side} {amount:.4f} @ {price:.4f}, ReduceOnly={is_reduce_only}")
            
        except Exception as e:
            logger.error(f"下单失败: {side} {amount} @ {price}, 原因: {e}")
            return

    async def cancel_order(self, client_order_id):
        """根据自定义的客户端订单ID取消订单。"""
        try:
            # 从本地缓存中查找订单的交易所ID
            order_to_cancel = self.open_orders.get(client_order_id)
            if order_to_cancel:
                exchange_order_id = order_to_cancel['i'] # 'i' 是币安返回的订单ID字段
                await self.exchange.cancel_order(exchange_order_id, self.symbol)
                logger.info(f"订单已请求撤销: ClientID={client_order_id}, ExchangeID={exchange_order_id}")
            
        except ccxt.OrderNotFound:
            logger.warning(f"尝试取消一个不存在或已处理的订单: {client_order_id}")
            # 如果订单未找到，可能已经被处理，从本地缓存中移除是安全的
            if client_order_id in self.open_orders:
                del self.open_orders[client_order_id]
            return
        except Exception as e:
            logger.error(f"撤单失败 (ClientID: {client_order_id}): {e}")
            return
    
    async def cancel_all_open_orders(self):
        """取消指定交易对的所有挂单，通常在启动或关闭时调用。"""
        try:
            logger.info(f"准备撤销所有 {self.symbol} 的挂单...")
            # 直接调用交易所的批量撤单功能，效率更高
            await self.exchange.cancel_all_orders(self.symbol)
            self.open_orders.clear()
            logger.info(f"已发送批量撤销 {self.symbol} 所有挂单的请求。")
            
        except Exception as e:
            logger.error(f"批量撤销所有订单失败: {e}")

    async def full_state_sync(self):
        """通过REST API完全同步持仓和挂单状态，作为WebSocket数据流的补充和校准。"""
        async with self.lock:
            try:
                logger.info("正在执行状态完全同步(REST API)...")
                
                # 并发获取持仓、挂单和最新价格
                positions, open_orders_raw, ticker = await asyncio.gather(
                    self.exchange.fetch_positions([self.symbol]),
                    self.exchange.fetch_open_orders(self.symbol),
                    self.exchange.fetch_ticker(self.symbol)
                )

                self.last_price = ticker['last']

                # 解析持仓数据
                long_pos = next((p for p in positions if p['info']['positionSide'] == 'LONG'), None)
                short_pos = next((p for p in positions if p['info']['positionSide'] == 'SHORT'), None)
                self.long_position_size = float(long_pos['contracts']) if long_pos and long_pos['contracts'] else 0.0
                self.short_position_size = float(short_pos['contracts']) if short_pos and short_pos['contracts'] else 0.0
                self.net_inventory = self.long_position_size - self.short_position_size

                # 更新本地挂单缓存
                self.open_orders = {o['clientOrderId']: o['info'] for o in open_orders_raw if 'clientOrderId' in o}
                
                self.last_state_sync_time = time.time()
                logger.info(f"状态同步完成。持仓: L={self.long_position_size}, S={self.short_position_size}. 挂单数: {len(self.open_orders)}")
            except Exception as e:
                logger.error(f"状态完全同步失败: {e}", exc_info=True)


    # --------------------------------------------------------------------------
    # --- 辅助与计算函数 ---
    # --------------------------------------------------------------------------
    async def update_dynamic_parameters(self):
        """更新策略所需的动态计算参数，如价差、公允价值、基础下单量等。"""
        # 1. 根据最新价格更新基础下单量
        if self.last_price > 0:
            self.base_quantity = self.config['initial_value'] / self.last_price
        
        # 2. 如果启用动态价差，则计算ATR并更新价差和公允价值
        if self.config['grid']['dynamic_spacing_enabled']:
            try:
                # 设置超时以防API长时间无响应
                ohlcv = await asyncio.wait_for(
                    self.exchange.fetch_ohlcv(
                        self.symbol, 
                        self.config['grid']['atr_timeframe'], 
                        limit=self.config['grid']['atr_period'] + 5 # 多获取几根K线以确保计算准确
                    ),
                    timeout=10
                )
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                
                # 计算真实波幅 (TR) 和平均真实波幅 (ATR)
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift())
                low_close = abs(df['low'] - df['close'].shift())
                tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = tr.rolling(window=self.config['grid']['atr_period']).mean().iloc[-1]
                self.grid_spacing_pct = (atr / self.last_price) * self.config['grid']['atr_multiplier']
                
                # 使用指数移动平均线 (EMA) 作为公允价值的估计
                ema_period = self.config['grid']['fair_price_ema_period']
                self.fair_price = df['close'].ewm(span=ema_period, adjust=False).mean().iloc[-1]

            except (asyncio.TimeoutError, ccxt.RequestTimeout, ccxt.NetworkError) as e:
                logger.warning(f"获取K线数据超时或网络错误，暂时使用固定的价差和公允价: {e}")
                self.grid_spacing_pct = self.config['grid']['base_spacing_pct']
                self.fair_price = self.last_price
            except Exception as e:
                logger.warning(f"计算动态参数时发生错误，暂时使用固定的价差和公允价: {e}")
                self.grid_spacing_pct = self.config['grid']['base_spacing_pct']
                self.fair_price = self.last_price
        else:
            # 如果禁用动态价差，则使用配置中的固定值
            self.grid_spacing_pct = self.config['grid']['base_spacing_pct']
            self.fair_price = self.last_price

    async def set_hedge_mode(self):
        """设置账户为双向持仓（对冲）模式。"""
        try:
            await self.exchange.set_position_mode(hedged=True, symbol=self.symbol)
            logger.info("双向持仓模式已成功设置或确认。")
        except Exception as e:
            # 很多交易所如果模式已正确，会报错提示无需更改，这里捕获并忽略这类“错误”
            if 'No need to change position side' in str(e):
                logger.info("当前已是双向持仓模式，无需更改。")
            else:
                logger.error(f"设置双向持仓模式失败: {e}")
                raise

    async def set_leverage(self):
        """根据配置设置杠杆倍数。"""
        try:
            await self.exchange.set_leverage(self.config['leverage'], self.symbol)
            logger.info(f"杠杆已设置为 {self.config['leverage']}x")
        except Exception as e:
            logger.error(f"设置杠杆失败: {e}")

    async def fetch_listen_key(self):
        """获取用于WebSocket用户数据流的监听密钥。"""
        try:
            response = await self.exchange.fapiPrivatePostListenKey()
            listen_key = response.get('listenKey')
            if not listen_key:
                raise ValueError("从交易所获取的 listenKey 为空")
            logger.info(f"成功获取新的 listen key: {listen_key[:10]}...")
            return listen_key
        except Exception as e:
            logger.error(f"获取 listen key 失败: {e}")
            raise e

    async def keep_listen_key_alive(self):
        """后台任务，定期延长 listenKey 的有效期，防止WebSocket断开。"""
        while not self.is_shutting_down:
            try:
                await asyncio.sleep(1800)  # 币安的listenKey有效期为60分钟，我们每30分钟延长一次
                await self.exchange.fapiPrivatePutListenKey({'listenKey': self.listen_key})
                logger.info(f"ListenKey 已成功延长: {self.listen_key[:10]}...")
            except Exception as e:
                logger.error(f"延长 listenKey 失败: {e}")
                # 失败后可以尝试重新获取一个新的key
                try:
                    self.listen_key = await self.fetch_listen_key()
                    logger.info("已重新获取一个新的ListenKey以应对延长失败的情况。")
                except Exception as fetch_e:
                    logger.error(f"重新获取ListenKey也失败了: {fetch_e}")
                    await asyncio.sleep(60) # 等待一段时间再重试


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    """主程序入口函数，负责初始化和运行机器人。"""
    bot = None
    
    try:
        logger.info("启动自适应库存管理做市程序...")
        bot = AdaptiveMarketMakerBot(CONFIG)
        await bot.run()
        
    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)，开始优雅关闭...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            try:
                await bot.close()
            except Exception as e:
                logger.error(f"关闭程序时发生错误: {e}")
    
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    # 在Windows上，为避免某些asyncio相关的错误，建议显式设置事件循环策略
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())