# ------------------------------------------------------------------------------------
# 自适应库存管理做市策略 V1.1 (已修复)
# ------------------------------------------------------------------------------------
# 作者: Lens (由AI修复和优化)
# 日期: 2025-07-10
# 描述: 本脚本实现了一个基于主动库存管理、动态价差(ATR)和公允价值(EMA)的高级做市策略。
#              它会根据净库存水平自动在三种操作模式之间切换。
# 修复记录:
# V1.1: 修复了因交易所类使用错误(ccxt.binance)导致的API超时问题。
#       - 动态选择正确的合约交易类 (binanceusdm/binancecoinm)。
#       - 动态生成正确的WebSocket URL。
#       - 动态调用正确的Listen Key API。
#       - 修正了订单取消逻辑中的ID字段错误。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import ccxt  # 使用标准版的CCXT
import math
import os
import sys
import uuid
import pandas as pd
import hmac
import hashlib

# ====================================================================================
# --- 自定义交易所类 ---
# ====================================================================================
# 【修复】移除了固定的父类，将在初始化时动态创建
# class CustomGate(ccxt.binance): ...

# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "sny1wPg5BNQgqQ5NMIHzThH5V3wD0d2NBdQzXyxBDl36c9eDZuv0JWzQLbuiyBZH",      # ❗❗❗ 替换为你的 API Key
    "api_secret": "xMMHEt6Mwc8um5vLrRCJuM3F4n6n8ToUhJJwAtRKCmYHHAUYxpUKKauuEfMbdzfq",  # ❗❗❗ 替换为你的 API Secret
    "use_proxy": False,                 # 是否使用代理 (True/False)
    "proxy_url": "http://127.0.0.1:7897", # 代理服务器地址

    # --- 策略核心参数 ---
    "coin_name": "ETH",                 # 交易币种
    "contract_type": "USDC",            # 合约类型: USDC 或 USDT
    "leverage": 20,                     # 杠杆倍数
    "initial_value": 100,               # ✅ 单笔订单的基础名义价值 (USDC / USDT)

    # --- 报价与网格逻辑 ---
    "grid": {
        "dynamic_spacing_enabled": True,       # 是否启用基于ATR的动态间距
        "atr_period": 14,                      # ATR计算周期 (K线数量)
        "atr_timeframe": '5m',                 # ATR计算使用的K线周期
        "atr_multiplier": 0.6,                 # ✅ ATR波动率乘数，越大价差越宽，越安全
        "base_spacing_pct": 0.0015,            # 如果禁用动态间距，使用的固定间距 (0.15%)
        "fair_price_ema_period": 20            # 计算公允价值的EMA周期
    },

    # --- 风险与库存管理 ---
    "risk_management": {
        # ✅ 净头寸合约数量的硬性上限 (多-空)，这是最重要的风控参数
        "position_limit_contracts": 0.5,
        # ✅ 库存偏斜因子，越大，库存对报价影响越大，回归越快但可能错过成交
        "inventory_skew_factor": 0.001,
        # ✅ 订单调整阈值，价格偏离达到价差的多少比例时调整订单 (0.25 = 25%)
        "rebalance_threshold_pct": 0.25,
        # ✅ 防御模式触发阈值，净库存达到硬性上限的百分之多少时进入
        "defense_mode_trigger_pct": 0.5,
        # ✅ 安全补仓单距离系数，在防御模式下，补仓单挂在几倍ATR价差之外
        "defensive_add_spacing_multiplier": 2.0,
    },

    # --- 系统运行参数 ---
    "system": {
        "sync_interval_seconds": 60,           # REST API状态完全同步的间隔
        "timeout_seconds": 30,                 # API请求超时时间
        "ticker_update_interval_ms": 200,      # Ticker更新处理的最小间隔(毫秒)，防止过于频繁地调整订单
        "max_connection_retries": 5,           # 最大连接重试次数
        "connection_retry_delay": 5,           # 连接重试延迟（秒）
        "heartbeat_interval": 30,              # WebSocket心跳间隔（秒）
        "state_sync_on_error": True,           # 发生错误时是否强制进行状态同步
        "emergency_sync_interval": 300,        # 紧急状态同步间隔（秒），用于处理极端情况
        "api_retry_count": 3,                  # API请求重试次数
        "api_retry_delay": 1,                  # API请求重试基础延迟(秒)
        "recv_window_ms": 10000,               # 交易所API接收窗口(毫秒)
        "websocket_ping_interval": 20,         # WebSocket发送ping间隔(秒)
        "websocket_timeout": 60                # WebSocket接收超时(秒)
    }
}


# ====================================================================================
# --- 日志配置 ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class AdaptiveMarketMakerBot:
    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()  # 异步锁，用于保护共享状态的并发访问

        # --- 交易所与市场信息 ---
        self.exchange = self._initialize_exchange()
        self.symbol = f"{config['coin_name']}/{config['contract_type']}:{config['contract_type']}"
        self.market = None
        
        # 【修复】根据合约类型确定WebSocket基础URL
        contract_type = self.config['contract_type'].upper()
        if contract_type == 'USDT':
            self.websocket_base_url = "wss://fstream.binance.com"
        elif contract_type == 'USDC':
            self.websocket_base_url = "wss://dstream.binance.com"
        else:
            logger.error(f"不支持的合约类型: {contract_type}")
            sys.exit(1)
        logger.info(f"已设置WebSocket URL: {self.websocket_base_url}")


        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0

        # --- 核心策略状态 (单一事实来源) ---
        self.long_position_size = 0.0
        self.short_position_size = 0.0
        self.net_inventory = 0.0
        self.open_orders = {}  # key: clientOrderId, value: 统一订单对象

        # --- 动态策略参数 ---
        self.base_quantity = 0.0       # 基础下单量 (根据initial_value和价格计算)
        self.grid_spacing_pct = 0.0    # 当前网格价差百分比 (可能基于ATR动态变化)
        self.fair_price = 0.0          # 公允价值 (通常基于EMA计算)

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_ticker_time = 0
        self.is_shutting_down = False

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if "YOUR_API_KEY" in self.config['api_key'] or "YOUR_API_SECRET" in self.config['api_secret']:
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        # 【修复】参考grid_BN_ETH_Real.py的实现
        class CustomGate(ccxt.binance):
            def fetch(self, url, method='GET', headers=None, body=None):
                if headers is None:
                    headers = {}
                # 添加用户代理头，提高连接稳定性
                headers['User-Agent'] = 'Mozilla/5.0'
                return super().fetch(url, method, headers, body)

        proxies = None
        if self.config['use_proxy']:
            proxies = {'http': self.config['proxy_url'], 'https': self.config['proxy_url']}
            logger.info(f"已设置代理: {self.config['proxy_url']}")

        contract_type = self.config['contract_type'].upper()
        options = {
            'defaultType': 'delivery' if contract_type == 'USDC' else 'future',
            'adjustForTimeDifference': True,
            'recvWindow': 5000,
            'warnOnFetchOpenOrdersWithoutSymbol': False,
            'createMarketBuyOrderRequiresPrice': False
        }

        exchange = CustomGate({
            'apiKey': self.config['api_key'],
            'secret': self.config['api_secret'],
            'timeout': self.config['system']['timeout_seconds'] * 1000,
            'enableRateLimit': True,
            'options': options,
            'proxies': proxies
        })
        
        logger.info(f"交易所实例创建成功，合约类型: {contract_type}")
        return exchange
    
    async def test_connection(self):
        """测试网络连接和API可用性"""
        try:
            logger.info("正在测试API连接...")
            
            # 直接使用同步方法，异步通过asyncio.to_thread调用
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            # 测试交易所状态
            status = await run_sync(self.exchange.fetch_status)
            if status and status['status'] == 'ok':
                logger.info(f"交易所状态检查成功: {status}")
            else:
                logger.warning(f"交易所状态检查返回异常结果: {status}")
                
            # 测试市场数据接口
            try:
                ticker = await run_sync(self.exchange.fetch_ticker, self.symbol)
                if ticker and 'last' in ticker:
                    logger.info(f"成功获取行情数据: 最新价格 {ticker['last']}")
                else:
                    logger.warning(f"行情数据获取异常: {ticker}")
            except Exception as e:
                logger.error(f"获取行情数据失败: {e}")
                return False
                
            # 测试账户接口
            try:
                balance = await run_sync(self.exchange.fetch_balance)
                if balance:
                    usdc_balance = balance.get(self.config['contract_type'], {}).get('free', 0)
                    logger.info(f"成功获取账户余额: {self.config['contract_type']} = {usdc_balance}")
                else:
                    logger.warning("账户余额数据获取异常")
            except Exception as e:
                logger.error(f"获取账户余额失败: {e}")
                if "Invalid API-key" in str(e):
                    logger.error("API密钥无效或未授权交易权限")
                return False
                
            logger.info("所有API测试通过")
            return True
                
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
            
    # --------------------------------------------------------------------------
    # --- 启动与生命周期管理 ---
    # --------------------------------------------------------------------------
    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        
        # 定义异步执行同步函数的辅助方法
        async def run_sync(func, *args, **kwargs):
            return await asyncio.to_thread(func, *args, **kwargs)
        
        try:
            # 测试API连接
            api_ok = await self.test_connection()
            if not api_ok:
                raise Exception("API连接测试失败，请检查日志了解详细信息")
            
            logger.info("正在加载市场数据...")
            
            # 先尝试加载指定的交易对，避免加载全部市场
            try:
                market_info = await run_sync(lambda: self.exchange.market(self.symbol))
                if market_info:
                    logger.info(f"成功加载交易对信息: {self.symbol}")
                else:
                    # 如果指定交易对加载失败，尝试加载全部市场
                    logger.warning("无法直接加载指定交易对，尝试加载全部市场数据")
                    await run_sync(self.exchange.load_markets, True)
            except Exception as e:
                logger.warning(f"指定交易对加载失败，尝试加载全部市场数据: {e}")
                await run_sync(self.exchange.load_markets, True)
            
            # 验证交易对是否存在
            markets = self.exchange.markets
            if not markets or len(markets) == 0:
                raise Exception("Markets数据为空，加载失败")
            
            if self.symbol not in markets:
                raise Exception(f"交易对 {self.symbol} 不存在于市场数据中")
            
            self.market = self.exchange.market(self.symbol)
            logger.info(f"成功加载市场数据，合约精度: {self.market['precision']}")
            
            # 设置双向持仓模式和杠杆
            await self.set_hedge_mode()
            await self.set_leverage()
            
            # 同步持仓和订单状态
            await self.full_state_sync()
            
            if self.last_price == 0:
                ticker = await run_sync(self.exchange.fetch_ticker, self.symbol)
                self.last_price = ticker['last']
            
            # 计算动态参数
            await self.update_dynamic_parameters()
            
            # 获取listenKey并设置保活任务
            self.listen_key = await self.fetch_listen_key()
            asyncio.create_task(self.keep_listen_key_alive())
            
            logger.info("初始化设置完成。")
            logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}, 净库存={self.net_inventory:.4f}")
            logger.info(f"初始下单量={self.base_quantity}, 初始网格价差={self.grid_spacing_pct:.4%}")
            
        except Exception as e:
            logger.error(f"连接交易所失败: {e}")
            logger.info("请检查网络连接、API密钥权限或代理服务器设置。")
            raise
        
        self.market = self.exchange.market(self.symbol)
        
        await self.set_hedge_mode()
        await self.set_leverage()
        await self.full_state_sync()
        
        if self.last_price == 0:
            ticker = await self.exchange.fetch_ticker(self.symbol)
            self.last_price = ticker['last']

        await self.update_dynamic_parameters()
        self.listen_key = await self.fetch_listen_key()
        
        asyncio.create_task(self.keep_listen_key_alive())
        logger.info("初始化设置完成。")
        logger.info(f"当前持仓: 多头={self.long_position_size}, 空头={self.short_position_size}, 净库存={self.net_inventory:.4f}")
        logger.info(f"初始下单量={self.base_quantity}, 初始网格价差={self.grid_spacing_pct:.4%}")

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        for attempt in range(3):  # 最多尝试3次初始化
            try:
                logger.info(f"正在进行第{attempt+1}次初始化尝试...")
                await self.setup()
                logger.info("初始化成功，正在启动网格策略...")
                break  # 如果成功则跳出循环
            except Exception as e:
                logger.error(f"初始化失败 (尝试 {attempt+1}/3): {e}")
                if attempt == 2:  # 最后一次尝试
                    logger.error("三次初始化尝试均失败，程序退出")
                    return
                await asyncio.sleep(5)  # 等待5秒后重试
        
        try:
            # 初始化成功后，取消所有挂单
            await self.cancel_all_open_orders()
            logger.info("所有挂单已取消，准备重新挂单")
        except Exception as e:
            logger.error(f"取消挂单失败: {e}")
        
        max_retries = self.config['system']['max_connection_retries']
        retry_delay = self.config['system']['connection_retry_delay']
        websocket_ping = self.config['system']['websocket_ping_interval']
        websocket_timeout = self.config['system']['websocket_timeout']
        
        while not self.is_shutting_down:
            for attempt in range(max_retries):
                try:
                    # 修改WebSocket连接方式，参考grid_BN_ETH_Real.py
                    contract_type = self.config['contract_type'].upper()
                    coin_name = self.config['coin_name'].lower()
                    
                    # 直接连接到WebSocket基础URL
                    stream_url = self.websocket_base_url + "/ws"
                    
                    logger.info(f"正在连接WebSocket (尝试 {attempt + 1}/{max_retries}): {stream_url}")
                    
                    # 设置代理环境变量
                    if self.config['use_proxy']:
                        original_http_proxy = os.environ.get('http_proxy')
                        original_https_proxy = os.environ.get('https_proxy')
                        try:
                            os.environ['http_proxy'] = self.config['proxy_url']
                            os.environ['https_proxy'] = self.config['proxy_url']
                            logger.info(f"已设置WebSocket代理环境变量: {self.config['proxy_url']}")
                        except Exception as e:
                            logger.error(f"设置代理环境变量失败: {e}")
                    
                    async with websockets.connect(
                        stream_url, 
                        ping_interval=websocket_ping, 
                        ping_timeout=websocket_ping, 
                        close_timeout=10
                    ) as websocket:
                        logger.info("WebSocket 连接成功，准备订阅数据流。")
                        
                        # 恢复原始代理设置
                        if self.config['use_proxy']:
                            if original_http_proxy:
                                os.environ['http_proxy'] = original_http_proxy
                            else:
                                os.environ.pop('http_proxy', None)
                            if original_https_proxy:
                                os.environ['https_proxy'] = original_https_proxy
                            else:
                                os.environ.pop('https_proxy', None)
                        
                        # 订阅ticker数据（使用正确的交易对格式）
                        symbol_id = f"{coin_name}_{contract_type}" if contract_type == "USDC" else f"{coin_name}{contract_type.lower()}"
                        
                        # 使用类似grid_BN_ETH_Real.py的订阅方式
                        ticker_payload = {
                            "method": "SUBSCRIBE",
                            "params": [f"{symbol_id.lower()}@bookTicker"],
                            "id": 1
                        }
                        
                        await websocket.send(json.dumps(ticker_payload))
                        logger.info(f"已发送Ticker订阅请求: {ticker_payload}")
                        
                        # 等待并处理订阅确认
                        ticker_resp = await asyncio.wait_for(websocket.recv(), timeout=5)
                        logger.info(f"Ticker订阅响应: {ticker_resp}")
                        
                        # 订阅用户数据流
                        user_payload = {
                            "method": "SUBSCRIBE",
                            "params": [f"{self.listen_key}"],
                            "id": 2
                        }
                        
                        await websocket.send(json.dumps(user_payload))
                        logger.info(f"已发送用户数据流订阅请求")
                        
                        # 等待并处理订阅确认
                        user_resp = await asyncio.wait_for(websocket.recv(), timeout=5)
                        logger.info(f"用户数据流订阅响应: {user_resp}")
                        
                        # 连接成功后执行一次状态同步
                        await self.full_state_sync()
                        
                        while not self.is_shutting_down:
                            try:
                                message = await asyncio.wait_for(websocket.recv(), timeout=websocket_timeout)
                                data = json.loads(message)
                                
                                # 处理订阅确认消息
                                if isinstance(data, dict) and 'result' in data:
                                    logger.info(f"收到订阅确认: {data}")
                                    continue
                                
                                # 处理常规数据消息
                                if 'e' in data:  # 直接数据消息
                                    await self.handle_websocket_message(data)
                                elif 'data' in data:  # 复合流数据
                                    await self.handle_websocket_message(data['data'])
                                else:
                                    logger.debug(f"收到未识别格式的消息: {data}")
                            except asyncio.TimeoutError:
                                logger.warning("WebSocket接收消息超时，检查连接状态...")
                                try:
                                    # 发送ping帧检测连接
                                    pong = await websocket.ping()
                                    await asyncio.wait_for(pong, timeout=5)
                                    logger.debug("WebSocket ping成功，连接仍然活跃")
                                    # 发送心跳消息保持活跃
                                    ping_payload = {"ping": int(time.time() * 1000)}
                                    await websocket.send(json.dumps(ping_payload))
                                    continue
                                except Exception:
                                    logger.warning("WebSocket ping失败，连接可能已断开")
                                    break
                            except websockets.exceptions.ConnectionClosed as e:
                                logger.warning(f"WebSocket连接已关閉 ({e.code}): {e.reason}，尝试重新连接...")
                                # 如果是因为listenKey过期，重新获取
                                if "listenKey" in str(e.reason).lower():
                                    try:
                                        self.listen_key = await self.fetch_listen_key()
                                        logger.info(f"已重新获取ListenKey: {self.listen_key[:10]}...")
                                    except Exception as lk_err:
                                        logger.error(f"重新获取ListenKey失败: {lk_err}")
                                break
                    
                    if not self.is_shutting_down: break
                    
                except (websockets.exceptions.InvalidURI, 
                       websockets.exceptions.InvalidHandshake,
                       OSError, 
                       websockets.exceptions.ConnectionClosed) as e:
                    if self.is_shutting_down: break
                    
                    logger.error(f"WebSocket连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                    
                    if attempt == max_retries - 1:
                        logger.error("WebSocket连接失败次数过多，等待更长时间后重试...")
                        await asyncio.sleep(30)
                        break
                    
                    wait_time = retry_delay * (2 ** attempt)
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    
                    try:
                        # 重新获取listenKey并同步状态
                        self.listen_key = await self.fetch_listen_key()
                        await self.full_state_sync()
                        logger.info("已重新获取ListenKey和同步状态")
                    except Exception as e_lk:
                        logger.error(f"重新获取ListenKey或同步状态失败: {e_lk}")
                        continue
                        
                except Exception as e:
                    if self.is_shutting_down: break
                    logger.critical(f"主循环发生严重错误: {e}", exc_info=True)
                    
                    # 尝试重新连接交易所和同步状态
                    try:
                        await self.test_connection()
                        await self.full_state_sync()
                        logger.info("已重新测试连接和同步状态")
                    except Exception:
                        pass
                        
                    await asyncio.sleep(15)
                    break
            
            if self.is_shutting_down: break
    
    async def close(self):
        """优雅关闭程序，取消所有挂单并关闭连接"""
        if self.is_shutting_down: return
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        await self.cancel_all_open_orders()
        logger.info("交易所连接已关闭。")

    # --- Omitted for brevity: handle_websocket_message, handle_ticker_update, handle_order_update are unchanged ---
    async def handle_websocket_message(self, data):
        """处理来自WebSocket的消息，分发到不同的处理器"""
        event_type = data.get("e")
        if event_type == "bookTicker":
            current_time_ms = time.time() * 1000
            # 节流操作，避免过于频繁地处理ticker更新
            if current_time_ms - self.last_ticker_time < self.config['system']['ticker_update_interval_ms']:
                return
            self.last_ticker_time = current_time_ms
            await self.handle_ticker_update(data)

        elif event_type == "ORDER_TRADE_UPDATE":
            await self.handle_order_update(data)
            
        elif event_type == "listenKeyExpired":
            logger.warning("ListenKey 已过期，需要重连 WebSocket。")
            self.listen_key = await self.fetch_listen_key()
            # 抛出异常让主循环捕捉并进行重连
            raise websockets.exceptions.ConnectionClosed(1000, "ListenKey expired, must reconnect.")

    async def handle_ticker_update(self, data):
        """处理行情更新并触发策略调整"""
        self.best_bid_price = float(data['b'])
        self.best_ask_price = float(data['a'])
        new_last_price = (self.best_bid_price + self.best_ask_price) / 2
        
        # 仅在价格有显著变化时更新，避免过度计算
        if self.last_price > 0 and abs(new_last_price - self.last_price) / self.last_price > 0.0001:
            self.last_price = new_last_price
            await self.update_dynamic_parameters() # 价格变化时，重新计算ATR、公允价等

        # 定期完全同步状态，以防websocket消息丢失导致状态不一致
        if time.time() - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
            await self.full_state_sync()

        await self.adjust_grid_strategy() # 根据最新状态调整订单

    async def handle_order_update(self, data):
        """处理订单更新消息，维护本地订单和持仓状态"""
        order_data = data.get("o", {})
        if order_data.get("s") != self.market['id']:
            return # 忽略不相关的交易对
            
        async with self.lock:
            client_order_id = order_data['c']
            status = order_data['X']

            if status == "NEW":
                # 注意：此时订单对象可能不完整，最好以REST API同步为准
                if client_order_id not in self.open_orders:
                   self.open_orders[client_order_id] = {'info': order_data} # 存储原始信息
                logger.info(f"新订单: {order_data['S']} {order_data['ps']} {order_data['q']} @ {order_data['p']}, ID: {client_order_id}")
            
            elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                if client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]
                    logger.info(f"订单终结 ({status}): ID: {client_order_id}")

            elif status in ["FILLED", "PARTIALLY_FILLED"] and float(order_data['l']) > 0:
                filled_qty = float(order_data['l'])
                position_side = order_data['ps']
                side = order_data['S']

                # 此处直接更新持仓可能会有延迟或不一致，更好的做法是触发一次full_state_sync
                # 但为了实时性，我们先本地更新，再依赖定时同步来校准
                logger.info(f"✅ 订单成交事件: {side} {position_side} {filled_qty}. 触发状态同步以更新精确持仓...")
                await self.full_state_sync()
                # 如果不希望每次成交都同步，可以恢复以下本地计算逻辑，但风险更高
                # if position_side == 'LONG':
                #     self.long_position_size += filled_qty if side == 'BUY' else -filled_qty
                # elif position_side == 'SHORT':
                #     self.short_position_size += filled_qty if side == 'SELL' else -filled_qty
                # self.net_inventory = self.long_position_size - self.short_position_size
                # logger.info(f"✅ 订单成交: {side} {position_side} {filled_qty}. 最新持仓: 多={self.long_position_size:.4f}, 空={self.short_position_size:.4f}, 净库存={self.net_inventory:.4f}")
                
                if status == "FILLED" and client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]

    # --- Omitted for brevity: adjust_grid_strategy and its sub-logics are unchanged ---
    async def adjust_grid_strategy(self):
        """自适应库存管理模型：根据净库存切换策略模式"""
        async with self.lock:
            net_inventory = self.net_inventory
            hard_limit = self.config['risk_management']['position_limit_contracts']
            defense_trigger = hard_limit * self.config['risk_management']['defense_mode_trigger_pct']
            single_trade_qty = self.base_quantity

            # --- 模式切换逻辑 ---
            if abs(net_inventory) >= hard_limit:
                logger.warning(f"模式: 紧急处理! 净库存 {net_inventory:.4f} 已达到硬性上限 {hard_limit}！")
                await self.execute_emergency_exit_logic()
            
            elif abs(net_inventory) >= defense_trigger:
                logger.info(f"模式: 防御网格. 净库存: {net_inventory:.4f}")
                await self.execute_defensive_grid_logic()

            elif abs(net_inventory) >= single_trade_qty * 0.5:
                logger.info(f"模式: 标准偏斜. 净库存: {net_inventory:.4f}")
                await self.execute_standard_skew_logic()
            
            else:
                logger.info(f"模式: 中性寻源. 净库存: {net_inventory:.4f}")
                await self.execute_neutral_mode_logic()

    async def execute_neutral_mode_logic(self):
        """模式一：中性寻源。目标是快速建立初始库存或在库存接近于零时进行交易。"""
        # 采用极小的偏斜，挂出紧凑的双边订单以增加成交概率
        skew_factor = self.config['risk_management']['inventory_skew_factor'] * 0.1
        skewed_mid_price = self.fair_price * (1 - self.net_inventory * skew_factor)
        
        target_bid = skewed_mid_price * (1 - self.grid_spacing_pct)
        target_ask = skewed_mid_price * (1 + self.grid_spacing_pct)
        
        target_orders = [
            {'side': 'buy', 'price': target_bid, 'positionSide': 'LONG', 'type': 'NEUTRAL_BID'},
            {'side': 'sell', 'price': target_ask, 'positionSide': 'SHORT', 'type': 'NEUTRAL_ASK'}
        ]
        await self.rebalance_orders(target_orders)

    async def execute_standard_skew_logic(self):
        """模式二：标准偏斜。核心做市模式，通过库存偏斜来管理风险并赚取价差。"""
        skew_factor = self.config['risk_management']['inventory_skew_factor']
        # 核心公式：库存偏离中性水平越多，报价就越远离公允价值，以激励反向交易，使库存回归。
        skewed_mid_price = self.fair_price * (1 - self.net_inventory * skew_factor)
        
        target_bid = skewed_mid_price * (1 - self.grid_spacing_pct)
        target_ask = skewed_mid_price * (1 + self.grid_spacing_pct)
        
        target_orders = [
            {'side': 'buy', 'price': target_bid, 'positionSide': 'LONG', 'type': 'SKEW_BID'},
            {'side': 'sell', 'price': target_ask, 'positionSide': 'SHORT', 'type': 'SKEW_ASK'}
        ]
        await self.rebalance_orders(target_orders)

    async def execute_defensive_grid_logic(self):
        """模式三：防御网格。当库存达到较高水平时，停止逆向开仓，专注于安全地降低库存。"""
        target_orders = []
        spacing_val = self.fair_price * self.grid_spacing_pct
        
        if self.net_inventory > 0:  # 多头库存过高
            take_profit_price = self.best_bid_price + spacing_val
            target_orders.append({'side': 'sell', 'price': take_profit_price, 'positionSide': 'LONG', 'type': 'DEFENSIVE_TP_L'})
            
            multiplier = self.config['risk_management']['defensive_add_spacing_multiplier']
            add_price = self.fair_price - (spacing_val * multiplier)
            target_orders.append({'side': 'buy', 'price': add_price, 'positionSide': 'LONG', 'type': 'DEFENSIVE_ADD_L'})
            
        elif self.net_inventory < 0: # 空头库存过高
            take_profit_price = self.best_ask_price - spacing_val
            target_orders.append({'side': 'buy', 'price': take_profit_price, 'positionSide': 'SHORT', 'type': 'DEFENSIVE_TP_S'})
            
            multiplier = self.config['risk_management']['defensive_add_spacing_multiplier']
            add_price = self.fair_price + (spacing_val * multiplier)
            target_orders.append({'side': 'sell', 'price': add_price, 'positionSide': 'SHORT', 'type': 'DEFENSIVE_ADD_S'})
            
        await self.rebalance_orders(target_orders)

    async def execute_emergency_exit_logic(self):
        """紧急处理模式：当净库存触及硬性上限时，不再进行任何开仓，只允许平仓。"""
        target_orders = []
        if self.net_inventory > 0:
            target_orders.append({'side': 'sell', 'price': self.best_bid_price, 'positionSide': 'LONG', 'type': 'EMERGENCY_EXIT_L'})
        elif self.net_inventory < 0:
            target_orders.append({'side': 'buy', 'price': self.best_ask_price, 'positionSide': 'SHORT', 'type': 'EMERGENCY_EXIT_S'})
        
        await self.rebalance_orders(target_orders, quantity_override=abs(self.net_inventory))


    # --------------------------------------------------------------------------
    # --- 订单与状态管理 ---
    # --------------------------------------------------------------------------
    async def rebalance_orders(self, target_orders, quantity_override=None):
        """订单协调核心逻辑：对比目标订单与现有订单，执行新增、修改、删除操作。"""
        await self.sync_order_status()
        
        current_orders_copy = list(self.open_orders.values())
        rebalance_threshold = self.grid_spacing_pct * self.config['risk_management']['rebalance_threshold_pct']
        
        for order in current_orders_copy:
            client_order_id = order.get('clientOrderId')
            if not client_order_id: continue

            order_side = order['side']
            order_ps = order['info']['positionSide']
            order_price = order['price']

            match_found = False
            for target in list(target_orders):
                if target['side'] == order_side and target['positionSide'] == order_ps:
                    price_diff_pct = abs(order_price - target['price']) / target['price']
                    
                    if price_diff_pct > rebalance_threshold:
                        logger.info(f"订单价格需调整: {order_side} {order_ps}, 旧价:{order_price}, 新价:{target['price']:.4f}. 执行撤单...")
                        await self.cancel_order(client_order_id)
                    else:
                        match_found = True
                    
                    target_orders.remove(target) 
                    break

            if not match_found:
                logger.info(f"取消多余或过时的订单: {order_side} {order_ps} @ {order_price}")
                await self.cancel_order(client_order_id)

        for target in target_orders:
            quantity = quantity_override if quantity_override is not None else self.base_quantity
            logger.info(f"创建新的策略订单: {target['side']} {target['positionSide']} @ {target['price']:.4f}")
            await self.place_order(target['side'], target['positionSide'], quantity, target['price'])
    
    async def sync_order_status(self):
        """通过REST API同步本地订单记录与交易所的实际挂单状态。"""
        try:
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            exchange_orders = await run_sync(self.exchange.fetch_open_orders, self.symbol)
            # 使用统一的订单对象，而不仅仅是info
            self.open_orders = {o['clientOrderId']: o for o in exchange_orders if 'clientOrderId' in o}
            logger.debug(f"订单状态同步完成，当前开放订单数: {len(self.open_orders)}")
            
        except Exception as e:
            logger.warning(f"订单状态同步超时/网络错误: {e}")

    async def place_order(self, side, position_side, amount, price):
        """封装统一的下单请求，自动处理开仓/平仓逻辑。"""
        if amount <= self.market['limits']['amount']['min']:
            logger.warning(f"下单数量 {amount} 小于最小允许值 {self.market['limits']['amount']['min']}，跳过本次下单。")
            return
        
        is_reduce_only = False
        if (position_side == 'LONG' and side == 'sell' and self.long_position_size > 0) or \
           (position_side == 'SHORT' and side == 'buy' and self.short_position_size > 0):
            is_reduce_only = True
            if position_side == 'LONG':
                amount = min(amount, self.long_position_size)
            else:
                amount = min(amount, self.short_position_size)

        try:
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            client_order_id = f"x-amm-{int(time.time() * 1000)}-{uuid.uuid4().hex[:4]}"
            params = {'positionSide': position_side.upper(), 'newClientOrderId': client_order_id}
            if is_reduce_only:
                 params['reduceOnly'] = True
            
            await run_sync(self.exchange.create_order, self.symbol, 'limit', side, amount, price, params)
            logger.info(f"订单已提交: {side} {position_side} {amount:.4f} @ {price:.4f}, ReduceOnly={is_reduce_only}")
            
        except Exception as e:
            logger.error(f"下单失败: {side} {amount} @ {price}, 原因: {e}")

    async def cancel_order(self, client_order_id):
        """根据自定义的客户端订单ID取消订单。"""
        try:
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            order_to_cancel = self.open_orders.get(client_order_id)
            if order_to_cancel:
                # 使用统一结构中的 'id'，而不是原始信息中的 'i' 或 'orderId'
                exchange_order_id = order_to_cancel['id']
                await run_sync(self.exchange.cancel_order, exchange_order_id, self.symbol)
                logger.info(f"订单已请求撤销: ClientID={client_order_id}, ExchangeID={exchange_order_id}")
            
        except Exception as e:
            logger.error(f"撤单失败 (ClientID: {client_order_id}): {e}")
            if "Order does not exist" in str(e) or "order canceled or filled" in str(e).lower():
                logger.warning(f"尝试取消一个不存在或已处理的订单: {client_order_id}")
                if client_order_id in self.open_orders:
                    del self.open_orders[client_order_id]
    
    async def cancel_all_open_orders(self):
        """取消指定交易对的所有挂单。"""
        try:
            logger.info(f"准备撤销所有 {self.symbol} 的挂单...")
            
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            # 先尝试同步订单状态，确保知道有哪些订单需要取消
            try:
                await self.sync_order_status()
            except Exception as e:
                logger.warning(f"同步订单状态失败，将尝试直接批量撤单: {e}")
                
            # 然后取消所有订单
            if len(self.open_orders) > 0:
                logger.info(f"取消 {len(self.open_orders)} 个挂单...")
                await run_sync(self.exchange.cancel_all_orders, self.symbol)
                self.open_orders.clear()
                logger.info("所有挂单已成功取消")
            else:
                logger.info("当前没有挂单需要取消")
            
        except Exception as e:
            logger.error(f"批量撤销所有订单失败: {e}")
            
            # 如果批量撤单失败，尝试逐个取消订单
            if hasattr(self, 'open_orders') and self.open_orders:
                logger.warning("尝试逐个取消订单...")
                for order_id in list(self.open_orders.keys()):
                    try:
                        await self.cancel_order(order_id)
                        await asyncio.sleep(0.2)  # 避免API限流
                    except Exception as cancel_e:
                        logger.error(f"取消订单 {order_id} 失败: {cancel_e}")

    async def full_state_sync(self):
        """通过REST API完全同步持仓和挂单状态。"""
        async with self.lock:
            try:
                logger.info("正在执行状态完全同步(REST API)...")
                
                # 定义异步执行同步函数的辅助方法
                async def run_sync(func, *args, **kwargs):
                    return await asyncio.to_thread(func, *args, **kwargs)
                
                # 并行获取持仓、挂单和行情数据
                positions_future = run_sync(self.exchange.fetch_positions, [self.symbol])
                open_orders_future = run_sync(self.exchange.fetch_open_orders, self.symbol)
                ticker_future = run_sync(self.exchange.fetch_ticker, self.symbol)
                
                # 等待所有请求完成
                positions = await positions_future
                open_orders_raw = await open_orders_future
                ticker = await ticker_future

                self.last_price = ticker['last']

                long_pos = next((p for p in positions if p['info']['positionSide'] == 'LONG'), None)
                short_pos = next((p for p in positions if p['info']['positionSide'] == 'SHORT'), None)
                self.long_position_size = float(long_pos['contracts']) if long_pos and long_pos['contracts'] is not None else 0.0
                self.short_position_size = float(short_pos['contracts']) if short_pos and short_pos['contracts'] is not None else 0.0
                self.net_inventory = self.long_position_size - self.short_position_size

                # 使用统一的订单对象，并用clientOrderId作为键
                self.open_orders = {o['clientOrderId']: o for o in open_orders_raw if 'clientOrderId' in o}
                
                self.last_state_sync_time = time.time()
                logger.info(f"状态同步完成。持仓: L={self.long_position_size:.4f}, S={self.short_position_size:.4f}. 挂单数: {len(self.open_orders)}")
            except Exception as e:
                logger.error(f"状态完全同步失败: {e}", exc_info=True)


    # --- Omitted for brevity: update_dynamic_parameters is unchanged ---
    async def update_dynamic_parameters(self):
        """更新策略所需的动态计算参数，如价差、公允价值、基础下单量等。"""
        # 定义异步执行同步函数的辅助方法
        async def run_sync(func, *args, **kwargs):
            return await asyncio.to_thread(func, *args, **kwargs)
            
        # 1. 根据最新价格更新基础下单量
        if self.last_price > 0:
            self.base_quantity = self.config['initial_value'] / self.last_price
        
        # 2. 如果启用动态价差，则计算ATR并更新价差和公允价值
        if self.config['grid']['dynamic_spacing_enabled']:
            try:
                # 设置超时以防API长时间无响应
                ohlcv = await run_sync(
                    self.exchange.fetch_ohlcv,
                    self.symbol, 
                    self.config['grid']['atr_timeframe'], 
                    limit=self.config['grid']['atr_period'] + 5 # 多获取几根K线以确保计算准确
                )
                
                if not ohlcv: raise ValueError("获取的K线数据为空")
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                
                # 计算真实波幅 (TR) 和平均真实波幅 (ATR)
                high_low = df['high'] - df['low']
                high_close = abs(df['high'] - df['close'].shift())
                low_close = abs(df['low'] - df['close'].shift())
                tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                atr = tr.rolling(window=self.config['grid']['atr_period']).mean().iloc[-1]
                self.grid_spacing_pct = (atr / self.last_price) * self.config['grid']['atr_multiplier']
                
                # 使用指数移动平均线 (EMA) 作为公允价值的估计
                ema_period = self.config['grid']['fair_price_ema_period']
                self.fair_price = df['close'].ewm(span=ema_period, adjust=False).mean().iloc[-1]

            except Exception as e:
                logger.warning(f"计算动态参数时发生错误，暂时使用固定的价差和公允价: {e}")
                self.grid_spacing_pct = self.config['grid']['base_spacing_pct']
                self.fair_price = self.last_price
        else:
            # 如果禁用动态价差，则使用配置中的固定值
            self.grid_spacing_pct = self.config['grid']['base_spacing_pct']
            self.fair_price = self.last_price
            
    async def set_hedge_mode(self):
        """设置账户为双向持仓（对冲）模式。"""
        try:
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            # 基于grid_BN_ETH_Real.py中的实现
            params = {"dualSidePosition": "true"}
            # 根据不同合约类型执行不同的API调用
            try:
                if self.config['contract_type'].upper() == 'USDT':
                    await run_sync(lambda: self.exchange.fapiPrivatePostPositionSideDual(params))
                else:
                    await run_sync(lambda: self.exchange.dapiPrivatePostPositionSideDual(params))
                logger.info("双向持仓模式已成功设置。")
            except Exception as e:
                if 'code=-4059' in str(e) or 'No need to change position side' in str(e) or '10060' in str(e):
                    logger.info("当前已是双向持仓模式，无需更改。")
                else:
                    raise
        except Exception as e:
            logger.error(f"设置双向持仓模式失败: {e}")
            logger.warning("请确保您在币安网站上已手动设置为'双向持仓'模式")

    async def set_leverage(self):
        """根据配置设置杠杆倍数。"""
        try:
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
                
            # 设置杠杆
            contract_type = self.config['contract_type'].upper()
            
            # 修正symbol格式
            if contract_type == 'USDT':
                symbol = f"{self.config['coin_name']}{contract_type}"
                endpoint = "fapiPrivatePostLeverage"
            else:  # USDC合约使用不同的格式
                symbol = f"{self.config['coin_name']}_{contract_type}"
                endpoint = "dapiPrivatePostLeverage"
            
            params = {
                "symbol": symbol,
                "leverage": self.config['leverage']
            }
            
            logger.info(f"设置杠杆，使用正确symbol格式: {symbol}")
            
            await run_sync(lambda: getattr(self.exchange, endpoint)(params))
            logger.info(f"杠杆已设置为 {self.config['leverage']}x")
        except Exception as e:
            if 'code=-4046' in str(e) or 'leverage already equals to the target value' in str(e):
                logger.info(f"杠杆已经是 {self.config['leverage']}x，无需更改")
            else:
                logger.error(f"设置杠杆失败: {e}")
                logger.warning(f"继续使用当前杠杆，这可能会影响计算")

    async def fetch_listen_key(self):
        """获取用于WebSocket用户数据流的监听密钥。"""
        try:
            logger.info("正在获取新的Listen Key...")
            
            # 定义异步执行同步函数的辅助方法
            async def run_sync(func, *args, **kwargs):
                return await asyncio.to_thread(func, *args, **kwargs)
            
            # 根据合约类型确定正确的API路径
            contract_type = self.config['contract_type'].upper()
            
            if contract_type == 'USDT':
                endpoint = "/fapi/v1/listenKey"
                base_url = "https://fapi.binance.com"
            elif contract_type == 'USDC':
                endpoint = "/dapi/v1/listenKey" 
                base_url = "https://dapi.binance.com"
            else:
                raise ValueError(f"不支持的合约类型: {contract_type}")
                
            url = base_url + endpoint
            
            # 手动创建签名
            timestamp = str(int(time.time() * 1000))
            queryString = f"timestamp={timestamp}"
            signature = hmac.new(
                self.config['api_secret'].encode('utf-8'),
                queryString.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            queryString = queryString + "&signature=" + signature
            
            headers = {
                'X-MBX-APIKEY': self.config['api_key']
            }
            
            # 使用aiohttp直接发送请求
            import aiohttp
            try:
                timeout = aiohttp.ClientTimeout(total=10)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 使用POST方法获取listenKey
                    async with session.post(
                        url, 
                        headers=headers, 
                        proxy=self.config['proxy_url'] if self.config['use_proxy'] else None
                    ) as response:
                        if response.status != 200:
                            # 如果出错，尝试用备用端点
                            error_text = await response.text()
                            logger.warning(f"获取listenKey失败，状态码: {response.status}, 响应: {error_text}")
                            
                            # 尝试使用API基本路径代替详细路径
                            logger.info(f"尝试使用替代端点获取listenKey...")
                            
                            # USDC合约的替代路径
                            if contract_type == 'USDC':
                                alt_endpoint = "/dapi/v1/listenKey"
                            else:
                                alt_endpoint = "/fapi/v1/listenKey"
                                
                            alt_url = base_url + alt_endpoint
                            
                            async with session.post(
                                alt_url, 
                                headers=headers, 
                                proxy=self.config['proxy_url'] if self.config['use_proxy'] else None
                            ) as alt_response:
                                if alt_response.status != 200:
                                    raise ValueError(f"替代端点获取listenKey也失败，状态码: {alt_response.status}")
                                data = await alt_response.json()
                        else:
                            data = await response.json()
                            
                        listen_key = data.get('listenKey')
            except Exception as e:
                raise ValueError(f"获取listenKey请求失败: {e}")
            
            if not listen_key:
                raise ValueError("从交易所获取的 listenKey 为空")
            logger.info(f"成功获取新的 listen key: {listen_key[:10]}...")
            return listen_key
        except Exception as e:
            logger.error(f"获取 listen key 失败: {e}", exc_info=True)
            # 【修复】增加错误处理建议
            if "APIError" in str(e):
                logger.error("API错误: 请检查API密钥权限是否包含合约交易权限，或者API密钥是否已过期")
            elif "Timeout" in str(e):
                logger.error("网络超时: 请检查代理设置或网络连接")
            raise

    async def keep_listen_key_alive(self):
        """后台任务，定期延长 listenKey 的有效期。"""
        while not self.is_shutting_down:
            try:
                await asyncio.sleep(1800)  # 每30分钟延长一次
                if self.listen_key:
                    logger.info(f"正在延长 ListenKey: {self.listen_key[:10]}...")
                    
                    # 根据合约类型确定正确的API路径
                    contract_type = self.config['contract_type'].upper()
                    
                    if contract_type == 'USDT':
                        endpoint = "/fapi/v1/listenKey"
                        base_url = "https://fapi.binance.com"
                    elif contract_type == 'USDC':
                        endpoint = "/dapi/v1/listenKey" 
                        base_url = "https://dapi.binance.com"
                    else:
                        raise ValueError(f"不支持的合约类型: {contract_type}")
                        
                    url = base_url + endpoint
                    
                    # 手动创建签名
                    timestamp = str(int(time.time() * 1000))
                    queryString = f"listenKey={self.listen_key}&timestamp={timestamp}"
                    signature = hmac.new(
                        self.config['api_secret'].encode('utf-8'),
                        queryString.encode('utf-8'),
                        hashlib.sha256
                    ).hexdigest()
                    
                    queryString = queryString + "&signature=" + signature
                    
                    headers = {
                        'X-MBX-APIKEY': self.config['api_key']
                    }
                    
                    # 使用aiohttp直接发送请求
                    import aiohttp
                    try:
                        timeout = aiohttp.ClientTimeout(total=10)
                        async with aiohttp.ClientSession(timeout=timeout) as session:
                            # 使用PUT方法延长listenKey
                            async with session.put(
                                url, 
                                params={"listenKey": self.listen_key},
                                headers=headers, 
                                proxy=self.config['proxy_url'] if self.config['use_proxy'] else None
                            ) as response:
                                if response.status != 200:
                                    error_text = await response.text()
                                    logger.warning(f"延长listenKey失败，尝试重新获取, 状态码: {response.status}, 响应: {error_text}")
                                    # 如果延长失败，尝试重新获取
                                    self.listen_key = await self.fetch_listen_key()
                                    logger.info(f"已重新获取ListenKey: {self.listen_key[:10]}...")
                                else:
                                    logger.info("ListenKey 已成功延长。")
                    except Exception as e:
                        logger.error(f"延长listenKey请求失败: {e}")
                        # 尝试重新获取
                        self.listen_key = await self.fetch_listen_key()
                        logger.info(f"因错误重新获取ListenKey: {self.listen_key[:10]}...")
                        
            except Exception as e:
                logger.error(f"延长 listenKey 失败: {e}")
                try:
                    self.listen_key = await self.fetch_listen_key()
                except Exception as fetch_e:
                    logger.error(f"重新获取ListenKey也失败了: {fetch_e}")
                    await asyncio.sleep(60)


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    """主程序入口函数，负责初始化和运行机器人。"""
    bot = None
    try:
        logger.info("启动自适应库存管理做市程序...")
        
        # 【检查】配置合约类型
        contract_type = CONFIG['contract_type'].upper()
        if contract_type not in ['USDT', 'USDC']:
            logger.error(f"不支持的合约类型: {contract_type}。请使用 'USDT' 或 'USDC'")
            return
            
        # 【检查】API端点可访问性
        logger.info("检查API端点可访问性...")
        base_url = "https://fapi.binance.com" if contract_type == "USDT" else "https://dapi.binance.com"
        ping_url = f"{base_url}/dapi/v1/ping" if contract_type == "USDC" else f"{base_url}/fapi/v1/ping"
        
        # 使用代理(如果配置了)进行预检
        import aiohttp
        proxy = None
        if CONFIG['use_proxy']:
            proxy = CONFIG['proxy_url']
            logger.info(f"使用代理 {proxy} 预检API连接...")
        
        # 测试API连接前先验证代理可用性
        if CONFIG['use_proxy']:
            try:
                logger.info("测试代理服务器可用性...")
                async with aiohttp.ClientSession() as session:
                    async with session.get("https://httpbin.org/ip", proxy=proxy, timeout=10) as response:
                        if response.status == 200:
                            proxy_check = await response.json()
                            logger.info(f"代理服务器工作正常，当前IP: {proxy_check.get('origin')}")
                        else:
                            logger.warning(f"代理服务器返回异常状态码: {response.status}")
            except Exception as e:
                logger.warning(f"代理服务器测试失败: {e}")
                logger.warning("将继续尝试直接连接，但可能会导致API访问问题")
        
        # 测试API端点
        try:
            logger.info(f"测试API端点 {ping_url}...")
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                start_time = time.time()
                async with session.get(ping_url, proxy=proxy) as response:
                    elapsed = time.time() - start_time
                    if response.status == 200:
                        logger.info(f"API端点 {ping_url} 访问成功，耗时: {elapsed:.2f}秒")
                    else:
                        logger.warning(f"API端点返回状态码 {response.status}，耗时: {elapsed:.2f}秒")
                        logger.warning("API端点测试失败，但仍将尝试启动")
        except Exception as e:
            logger.error(f"API端点访问失败: {e}")
            if "Cannot connect" in str(e) and CONFIG['use_proxy']:
                logger.error("无法连接到API端点，请检查代理设置是否正确")
                return
            logger.warning("API预检失败，但将继续尝试启动")
            
        # 创建并运行机器人实例
        bot = AdaptiveMarketMakerBot(CONFIG)
        await bot.run()
    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)，开始优雅关闭...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            try:
                await bot.close()
            except Exception as e:
                logger.error(f"关闭程序时发生错误: {e}")
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())