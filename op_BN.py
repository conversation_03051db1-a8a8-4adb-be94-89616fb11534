# ------------------------------------------------------------------------------------
# Delta趋势偏斜网格策略 V4.0 (Delta-Trend Skew Grid)
# ------------------------------------------------------------------------------------
# 作者: Lens & AI
# 日期: 2025-07-11
# 描述: 本策略融合了期权市场预期、多时间框架趋势分析和动态网格交易。
#       旨在通过在大概率区间内，顺着大趋势进行高频的、带有方向偏好的网格交易，
#       并设有终极风险熔断机制，以实现长期稳定的复利增长。
#
# 核心逻辑:
# 1. 趋势过滤: 使用4H 200EMA 和 1H 50EMA 确认主趋势方向。
# 2. 区间定义: 从Deribit获取期权数据，使用Delta 0.1~0.9的strike价格定义高概率交易区间。
# 3. 偏斜网格: 在区间内生成偏向趋势方向的买卖网格，实现“顺势积累，震荡撸短”。
# 4. 风险熔断: 当价格极端突破区间时，自动清仓止损，保护本金。
# ------------------------------------------------------------------------------------

import asyncio
import websockets
import json
import logging
import time
import ccxt
import math
import os
import sys
import uuid
import pandas as pd
import hmac
import hashlib
import aiohttp
from datetime import datetime, timedelta

# ====================================================================================
# --- 策略配置 (所有可调参数集中于此) ---
# ====================================================================================
CONFIG = {
    # --- 账户与连接配置 ---
    "api_key": "USlBuHx9zNwxXFtecEQZDOXXxbHhKMIViMeRaELIyjOHGCcdkoESgmxpu4bWi47c",      # ❗❗❗ 替换为你的币安 API Key
    "api_secret": "lxNiaI54wlWAyxpZ5oNajJ84MoQYodnakDWfoQiuONNTtU9YKsKS14tRwgBi7eUt",  # ❗❗❗ 替换为你的币安 API Secret
    "use_proxy": True,                      # 是否使用代理 (True/False)
    "proxy_url": "http://127.0.0.1:7897",    # 代理服务器地址

    # --- 交易对与合约配置 ---
    "coin_name": "BTC",                     # 交易币种 (必须在Deribit有对应期权)
    "contract_type": "USDT",                # 合约类型: 仅支持USDT或USDC
    "leverage": 10,                         # 杠杆倍数

    # --- 核心决策逻辑 ---
    "strategy_cycle_interval_seconds": 900, # 策略决策大循环的间隔 (15分钟)
    
    "trend_filter": {
        "enabled": True,
        "ema_fast_tf": '1h',                # 快速EMA时间框架
        "ema_fast_period": 50,              # 快速EMA周期
        "ema_slow_tf": '4h',                # 慢速EMA时间框架
        "ema_slow_period": 200,             # 慢速EMA周期
    },
    
    "delta_range": {
        "enabled": True,
        "option_expiry_days_min": 3,        # 寻找距离到期日大于X天的期权
        "option_expiry_days_max": 14,       # 寻找距离到期日小于X天的期权
        "upper_bound_delta": 0.15,          # 定义区间上界的Call期权Delta (越小区间越宽)
        "lower_bound_delta": 0.15,          # 定义区间下界的Put期权Delta (越小区间越宽)
        "deribit_base_url": "https://www.deribit.com/api/v2/public",
    },

    "grid_logic": {
        "total_capital_allocation_pct": 0.5, # ✅ 投入网格的总资金占账户余额的百分比 (例如0.5=50%)
        "grid_lines": 10,                   # ✅ 网格线总数 (买单数量)
        "profit_spread_pct": 0.005,         # ✅ 每格的止盈幅度 (0.005 = 0.5%)
        "skew_factor": 0.7,                 # ✅ 偏斜因子 (0.7 = 70%的网格挂在现价下方做多)
        "rebalance_threshold_pct": 0.25,    # 订单调整阈值 (网格间距的25%)
    },

    # --- 风险与熔断机制 ---
    "risk_management": {
        # ✅ 总持仓名义价值上限，占总余额的百分比 (例如2 = 200% = 2倍杠杆)
        "max_position_value_pct_of_balance": 2.0,
        "circuit_breaker_enabled": True,
        # ✅ 价格突破网格边界多少倍ATR后触发熔断
        "circuit_breaker_atr_multiplier": 1.5,
        "atr_period_for_breaker": 14,       # 用于熔断机制的ATR周期
        "atr_timeframe_for_breaker": '1h',  # 用于熔断机制的ATR时间框架
    },

    # --- 系统运行参数 (继承自V1.1，无需大改) ---
    "system": {
        "sync_interval_seconds": 300,          # REST API状态完全同步的间隔
        "timeout_seconds": 30,                 # API请求超时时间
        "ticker_update_interval_ms": 200,      # Ticker更新处理的最小间隔(毫秒)
        "max_connection_retries": 5,           # 最大连接重试次数
        "connection_retry_delay": 5,           # 连接重试延迟（秒）
        "websocket_ping_interval": 20,         # WebSocket发送ping间隔(秒)
        "websocket_timeout": 60,               # WebSocket接收超时(秒)
    }
}


# ====================================================================================
# --- 日志配置 ---
# ====================================================================================
if not os.path.exists("log"):
    os.makedirs("log")
script_name = os.path.splitext(os.path.basename(__file__))[0]
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler(f"log/{script_name}_{CONFIG['coin_name']}.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger()


# ====================================================================================
# --- 主策略类 ---
# ====================================================================================
class DeltaTrendGridBot:
    def __init__(self, config):
        """初始化策略实例"""
        self.config = config
        self.lock = asyncio.Lock()

        # --- 交易所与市场信息 ---
        self.exchange = self._initialize_exchange()
        self.symbol = f"{config['coin_name']}/{config['contract_type']}:{config['contract_type']}"
        self.market = None
        self.websocket_base_url = "wss://fstream.binance.com"

        # --- 实时数据状态 ---
        self.best_bid_price = 0.0
        self.best_ask_price = 0.0
        self.last_price = 0.0
        self.account_balance = 0.0

        # --- 核心策略状态 ---
        self.long_position_size = 0.0
        self.long_position_avg_price = 0.0
        self.short_position_size = 0.0 # 在本策略中，空头头寸主要用于平多
        self.total_position_contracts = 0.0
        self.open_orders = {}

        # --- 决策参数 ---
        self.current_trend = "SIDEWAYS" # 'UP', 'DOWN', 'SIDEWAYS'
        self.grid_lower_bound = 0.0
        self.grid_upper_bound = 0.0
        self.atr_value = 0.0

        # --- 系统控制变量 ---
        self.listen_key = None
        self.last_state_sync_time = 0
        self.last_strategy_cycle_time = 0
        self.last_ticker_time = 0
        self.is_shutting_down = False
        self.strategy_paused = False # 熔断后暂停策略

    # ... (从这里开始，大量继承自V1.1的优秀框架代码)

    def _initialize_exchange(self):
        """初始化并返回CCXT交易所实例"""
        if "YOUR_API_KEY" in self.config['api_key'] or "YOUR_API_SECRET" in self.config['api_secret']:
            logger.error("API密钥未配置，请在CONFIG中设置后再运行。")
            sys.exit(1)

        exchange_class = ccxt.binanceusdm

        class CustomGate(exchange_class):
            def fetch(self, url, method='GET', headers=None, body=None):
                if headers is None: headers = {}
                headers['User-Agent'] = 'Mozilla/5.0'
                return super().fetch(url, method, headers, body)

        proxies = {'http': self.config['proxy_url'], 'https': self.config['proxy_url']} if self.config['use_proxy'] else None

        exchange = CustomGate({
            'apiKey': self.config['api_key'],
            'secret': self.config['api_secret'],
            'timeout': self.config['system']['timeout_seconds'] * 1000,
            'enableRateLimit': True,
            'options': {'defaultType': 'future', 'adjustForTimeDifference': True},
            'proxies': proxies
        })
        logger.info(f"交易所实例创建成功，代理: {'启用' if proxies else '禁用'}")
        return exchange

    async def setup(self):
        """执行所有异步的启动设置"""
        logger.info("正在执行启动设置...")
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            await run_sync(self.exchange.load_markets, True)
            self.market = self.exchange.market(self.symbol)
            logger.info(f"成功加载市场数据, 合约精度: {self.market['precision']}")
            
            await self.set_hedge_mode()
            await self.set_leverage()
            await self.full_state_sync()
            
            if self.last_price == 0:
                ticker = await run_sync(self.exchange.fetch_ticker, self.symbol)
                self.last_price = ticker['last']

            # 首次运行策略决策循环
            await self.run_strategy_cycle()
            
            self.listen_key = await self.fetch_listen_key()
            asyncio.create_task(self.keep_listen_key_alive())
            
            logger.info("初始化设置完成。")
            logger.info(f"当前持仓: 多头={self.long_position_size}, 均价={self.long_position_avg_price}")
            logger.info(f"当前趋势: {self.current_trend}, 网格区间: [{self.grid_lower_bound:.2f} - {self.grid_upper_bound:.2f}]")

        except Exception as e:
            logger.error(f"启动设置失败: {e}", exc_info=True)
            raise

    async def run(self):
        """策略主循环，负责WebSocket连接和消息处理"""
        await self.setup()
        await self.cancel_all_open_orders()
        logger.info("所有旧挂单已取消，准备根据新策略部署。")
        
        while not self.is_shutting_down:
            try:
                stream_url = f"{self.websocket_base_url}/ws/{self.listen_key}"
                logger.info(f"正在连接WebSocket: {self.websocket_base_url}/ws/...")
                
                async with websockets.connect(stream_url, ping_interval=20, ping_timeout=20, close_timeout=10) as websocket:
                    logger.info("WebSocket 连接成功。")
                    
                    # 订阅ticker数据
                    ticker_payload = {"method": "SUBSCRIBE", "params": [f"{self.market['id'].lower()}@bookTicker"], "id": 1}
                    await websocket.send(json.dumps(ticker_payload))
                    logger.info(f"已发送Ticker订阅请求: {ticker_payload}")
                    
                    # 首次部署网格
                    await self.deploy_grid()
                    
                    while not self.is_shutting_down:
                        try:
                            message = await asyncio.wait_for(websocket.recv(), timeout=60)
                            data = json.loads(message)
                            await self.handle_websocket_message(data)
                        except asyncio.TimeoutError:
                            logger.debug("WebSocket接收消息超时，连接可能已断开...")
                            break
                        except websockets.exceptions.ConnectionClosed as e:
                            logger.warning(f"WebSocket连接已关闭 ({e.code}): {e.reason}")
                            break
            
            except Exception as e:
                logger.error(f"WebSocket主循环发生错误: {e}")
            
            if self.is_shutting_down: break
            logger.info(f"等待 {self.config['system']['connection_retry_delay']} 秒后重连...")
            await asyncio.sleep(self.config['system']['connection_retry_delay'])
            try:
                self.listen_key = await self.fetch_listen_key()
                logger.info("已重新获取ListenKey。")
            except Exception as e:
                logger.error(f"重连时获取ListenKey失败: {e}")

    async def close(self):
        """优雅关闭程序"""
        if self.is_shutting_down: return
        self.is_shutting_down = True
        logger.info("正在关闭程序...")
        await self.cancel_all_open_orders()
        logger.info("所有挂单已取消。")
        # 可选：添加市价平仓逻辑
        # await self.close_all_positions() 
        logger.info("程序已关闭。")

    # --- WebSocket 消息处理器 (继承并简化) ---
    async def handle_websocket_message(self, data):
        event_type = data.get("e")
        if event_type == "bookTicker":
            current_time_ms = time.time() * 1000
            if current_time_ms - self.last_ticker_time > self.config['system']['ticker_update_interval_ms']:
                self.last_ticker_time = current_time_ms
                await self.handle_ticker_update(data)
        elif event_type == "ORDER_TRADE_UPDATE":
            await self.handle_order_update(data['o'])
        elif event_type == "listenKeyExpired":
            logger.warning("ListenKey 已过期，需要重连 WebSocket。")
            raise websockets.exceptions.ConnectionClosed(1000, "ListenKey expired")

    async def handle_ticker_update(self, data):
        """处理行情更新，触发策略核心检查"""
        self.best_bid_price = float(data['b'])
        self.best_ask_price = float(data['a'])
        self.last_price = (self.best_bid_price + self.best_ask_price) / 2

        # 1. 周期性执行大循环决策
        current_time = time.time()
        if current_time - self.last_strategy_cycle_time > self.config['strategy_cycle_interval_seconds']:
            await self.run_strategy_cycle()
            await self.deploy_grid() # 决策更新后，重新部署网格
        
        # 2. 实时检查风险
        if self.config['risk_management']['circuit_breaker_enabled'] and not self.strategy_paused:
            await self.check_circuit_breaker()
        
        # 3. 定期同步，防止状态漂移
        if current_time - self.last_state_sync_time > self.config['system']['sync_interval_seconds']:
             await self.full_state_sync()

    async def handle_order_update(self, order_data):
        """处理订单更新，仅在成交时进行状态同步"""
        status = order_data['X']
        if status in ["FILLED", "PARTIALLY_FILLED"] and float(order_data['l']) > 0:
            logger.info(f"订单成交: {order_data['S']} {order_data['ps']} {order_data['q']} @ {order_data['p']}. 触发状态同步。")
            asyncio.create_task(self.full_state_sync_and_redeploy())

    async def full_state_sync_and_redeploy(self):
        """成交后执行状态同步，并立即重新部署网格以补充挂单"""
        await self.full_state_sync()
        await self.deploy_grid()

    # ====================================================================================
    # --- V4.0 核心决策逻辑 ---
    # ====================================================================================
    async def run_strategy_cycle(self):
        """策略决策大循环：更新所有决策参数"""
        if self.is_shutting_down or self.strategy_paused:
            return
        
        logger.info("="*20 + " 开始新一轮策略决策 " + "="*20)
        self.last_strategy_cycle_time = time.time()
        async with self.lock:
            try:
                # 按顺序更新决策所需的所有数据
                await self._fetch_and_set_trend()
                await self._fetch_and_set_delta_range()
                await self._fetch_and_set_atr()
                logger.info(f"决策更新完成。趋势: {self.current_trend}, 区间: [{self.grid_lower_bound:.2f}-{self.grid_upper_bound:.2f}], ATR: {self.atr_value:.2f}")
            except Exception as e:
                logger.error(f"策略决策周期中发生错误: {e}", exc_info=True)
        logger.info("="*20 + " 策略决策结束 " + "="*20)

    async def _fetch_and_set_trend(self):
        """获取K线并设置当前市场趋势"""
        if not self.config['trend_filter']['enabled']:
            self.current_trend = "UP" # 如果禁用过滤器，默认为上升趋势
            logger.info("趋势过滤器已禁用，默认趋势为 UP。")
            return
        
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            # 并行获取快慢周期的K线
            cfg_fast = self.config['trend_filter']
            ohlcv_fast_future = run_sync(self.exchange.fetch_ohlcv, self.symbol, cfg_fast['ema_fast_tf'], limit=cfg_fast['ema_slow_period'])
            
            cfg_slow = self.config['trend_filter']
            ohlcv_slow_future = run_sync(self.exchange.fetch_ohlcv, self.symbol, cfg_slow['ema_slow_tf'], limit=cfg_slow['ema_slow_period'])

            ohlcv_fast, ohlcv_slow = await asyncio.gather(ohlcv_fast_future, ohlcv_slow_future)
            
            if not ohlcv_fast or not ohlcv_slow:
                logger.warning("未能获取到足够的K线数据来判断趋势。")
                self.current_trend = "SIDEWAYS"
                return

            df_fast = pd.DataFrame(ohlcv_fast, columns=['ts', 'o', 'h', 'l', 'c', 'v'])
            df_slow = pd.DataFrame(ohlcv_slow, columns=['ts', 'o', 'h', 'l', 'c', 'v'])
            
            ema_fast = df_fast['c'].ewm(span=cfg_fast['ema_fast_period'], adjust=False).mean().iloc[-1]
            ema_slow = df_slow['c'].ewm(span=cfg_slow['ema_slow_period'], adjust=False).mean().iloc[-1]
            
            price = self.last_price
            
            old_trend = self.current_trend
            if price > ema_fast and price > ema_slow:
                self.current_trend = "UP"
            elif price < ema_fast and price < ema_slow:
                self.current_trend = "DOWN"
            else:
                self.current_trend = "SIDEWAYS"
            
            if old_trend != self.current_trend:
                logger.info(f"趋势发生变化: {old_trend} -> {self.current_trend}. Price={price:.2f}, EMA_Fast({cfg_fast['ema_fast_tf']})={ema_fast:.2f}, EMA_Slow({cfg_slow['ema_slow_tf']})={ema_slow:.2f}")

        except Exception as e:
            logger.error(f"判断趋势失败: {e}")
            self.current_trend = "SIDEWAYS" # 出错时默认为震荡

    async def _fetch_and_set_delta_range(self):
        """从Deribit获取期权数据，设置网格的上下边界"""
        if not self.config['delta_range']['enabled']:
            # 如果禁用，使用基于ATR的范围
            atr_range_multiplier = 3
            self.grid_lower_bound = self.last_price - self.atr_value * atr_range_multiplier
            self.grid_upper_bound = self.last_price + self.atr_value * atr_range_multiplier
            logger.info(f"Delta范围已禁用，使用ATR范围: [{self.grid_lower_bound:.2f} - {self.grid_upper_bound:.2f}]")
            return
            
        try:
            async with aiohttp.ClientSession() as session:
                # 1. 获取所有该币种的期权工具
                url = f"{self.config['delta_range']['deribit_base_url']}/get_instruments?currency={self.config['coin_name']}&kind=option&expired=false"
                async with session.get(url) as response:
                    instruments = (await response.json())['result']

                # 2. 筛选出符合到期日要求的期权
                today = datetime.utcnow()
                min_exp = today + timedelta(days=self.config['delta_range']['option_expiry_days_min'])
                max_exp = today + timedelta(days=self.config['delta_range']['option_expiry_days_max'])
                
                valid_instruments = [
                    inst for inst in instruments 
                    if min_exp < datetime.fromtimestamp(inst['expiration_timestamp']/1000) < max_exp
                ]
                
                if not valid_instruments:
                    raise ValueError("在指定日期范围内未找到Deribit期权。")
                
                # 3. 获取所有有效工具的报价摘要
                instrument_names = [inst['instrument_name'] for inst in valid_instruments]
                summary_url = f"{self.config['delta_range']['deribit_base_url']}/get_book_summary_by_instrument?instrument_name={','.join(instrument_names)}"
                async with session.get(summary_url) as response:
                    summaries = (await response.json())['result']

                # 4. 寻找最接近目标Delta的期权strike价格
                put_options = [s for s in summaries if s['instrument_name'].endswith('-P') and s['greeks']]
                call_options = [s for s in summaries if s['instrument_name'].endswith('-C') and s['greeks']]

                if not put_options or not call_options:
                    raise ValueError("未能获取到有效的期权希腊值数据。")

                # 寻找下边界 (Put Delta最接近目标值)
                target_put_delta = self.config['delta_range']['lower_bound_delta']
                closest_put = min(put_options, key=lambda x: abs(x['greeks']['delta'] + target_put_delta))
                self.grid_lower_bound = closest_put['instrument_name'].split('-')[2]
                self.grid_lower_bound = float(self.grid_lower_bound)

                # 寻找上边界 (Call Delta最接近目标值)
                target_call_delta = self.config['delta_range']['upper_bound_delta']
                closest_call = min(call_options, key=lambda x: abs(x['greeks']['delta'] - target_call_delta))
                self.grid_upper_bound = closest_call['instrument_name'].split('-')[2]
                self.grid_upper_bound = float(self.grid_upper_bound)
                
                if self.grid_lower_bound >= self.grid_upper_bound:
                    raise ValueError(f"计算出的Delta区间无效: Lower={self.grid_lower_bound}, Upper={self.grid_upper_bound}")

        except Exception as e:
            logger.error(f"获取Deribit Delta区间失败: {e}. 将使用上次的有效区间。")
            # 如果出错，不更新区间，沿用旧值

    async def _fetch_and_set_atr(self):
        """获取ATR值，用于风险熔断计算"""
        cfg = self.config['risk_management']
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            ohlcv = await run_sync(self.exchange.fetch_ohlcv, self.symbol, cfg['atr_timeframe_for_breaker'], limit=cfg['atr_period_for_breaker'] + 5)
            if not ohlcv: raise ValueError("获取K线数据为空")
            
            df = pd.DataFrame(ohlcv, columns=['ts', 'o', 'h', 'l', 'c', 'v'])
            high_low = df['h'] - df['l']
            high_close = abs(df['h'] - df['c'].shift())
            low_close = abs(df['l'] - df['c'].shift())
            tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            self.atr_value = tr.ewm(span=cfg['atr_period_for_breaker'], adjust=False).mean().iloc[-1]
        except Exception as e:
            logger.error(f"计算ATR失败: {e}. ATR值将为0，熔断机制可能失效。")
            self.atr_value = 0

    # ====================================================================================
    # --- V4.0 网格部署与风险控制 ---
    # ====================================================================================
    async def deploy_grid(self):
        """根据当前策略参数，生成目标订单并与现有订单协调"""
        if self.is_shutting_down or self.strategy_paused:
            logger.info("策略已暂停或正在关闭，跳过网格部署。")
            return

        async with self.lock:
            target_orders = await self.generate_target_grid()
            if not target_orders:
                logger.info("未生成目标订单，可能因趋势不明朗或持仓已满。将撤销所有挂单。")
                await self.cancel_all_open_orders()
                return
            
            await self.rebalance_orders(target_orders)
    
    async def generate_target_grid(self):
        """核心：生成目标网格订单列表"""
        if self.current_trend != "UP":
            # 当前牛市背景下，我们只在明确的上升趋势中操作
            logger.info(f"当前趋势为 {self.current_trend}，非上升趋势，不部署网格。")
            return []

        # 检查总持仓是否已达上限
        max_pos_value = self.account_balance * self.config['risk_management']['max_position_value_pct_of_balance']
        current_pos_value = self.long_position_size * self.last_price
        if current_pos_value >= max_pos_value:
            logger.warning(f"总持仓价值 {current_pos_value:.2f} 已达到或超过上限 {max_pos_value:.2f}，不再增加新买单。")
            # 此时可以只生成平仓单，但为简化，我们先停止所有新开仓
            return []
            
        target_orders = []
        cfg = self.config['grid_logic']
        
        # 计算网格参数
        price = self.last_price
        lower = max(self.grid_lower_bound, price * 0.8) # 安全措施，防止区间过大
        upper = min(self.grid_upper_bound, price * 1.2)
        
        # 定义买入和卖出的价格区域
        buy_zone_upper = price
        buy_zone_lower = lower
        
        # 计算单笔订单数量
        total_capital = self.account_balance * cfg['total_capital_allocation_pct']
        capital_per_line = total_capital / cfg['grid_lines']
        quantity_per_line = capital_per_line / price # 名义价值 / 价格 = 数量

        # 根据偏斜因子生成买单
        num_buy_grids_below = int(cfg['grid_lines'] * cfg['skew_factor'])
        num_buy_grids_above = cfg['grid_lines'] - num_buy_grids_below
        
        buy_prices_below = pd.Series(dtype='float64')
        if num_buy_grids_below > 0:
            buy_prices_below = pd.Series(pd.np.linspace(buy_zone_lower, buy_zone_upper, num_buy_grids_below + 1)[:-1])
        
        buy_prices_above = pd.Series(dtype='float64')
        if num_buy_grids_above > 0:
            buy_prices_above = pd.Series(pd.np.linspace(buy_zone_upper, upper, num_buy_grids_above + 2)[1:-1])
        
        buy_prices = pd.concat([buy_prices_below, buy_prices_above]).unique()

        # 为每个买单生成一个对应的止盈卖单
        for p_buy in buy_prices:
            p_sell = p_buy * (1 + cfg['profit_spread_pct'])
            
            # 添加买单
            target_orders.append({'side': 'buy', 'price': p_buy, 'positionSide': 'LONG', 'quantity': quantity_per_line})
            # 添加卖单(平多)
            # 注意：这里的卖单数量应与已成交的买单数量对应，但为简化，我们先挂等量的平仓单
            # 一个更复杂的系统会动态调整平仓单数量
            target_orders.append({'side': 'sell', 'price': p_sell, 'positionSide': 'LONG', 'quantity': quantity_per_line})
        
        logger.info(f"已生成 {len(buy_prices)} 组买卖网格订单。")
        return target_orders

    async def rebalance_orders(self, target_orders):
        """订单协调核心：对比目标与现有订单，执行增删改 (V1.1精华部分)"""
        current_orders_copy = list(self.open_orders.values())
        rebalance_threshold_pct = self.config['grid_logic']['rebalance_threshold_pct']
        
        orders_to_create = []
        orders_to_cancel = []
        orders_to_keep_ids = set()

        # 找出需要保留、取消的订单
        for target in target_orders:
            match_found = False
            for order in current_orders_copy:
                if (order['clientOrderId'] not in orders_to_keep_ids and
                    order['side'] == target['side'] and
                    order['info']['positionSide'] == target['positionSide']):
                    
                    price_diff_pct = abs(order['price'] - target['price']) / target['price']
                    grid_spacing = target['price'] * self.config['grid_logic']['profit_spread_pct']
                    rebalance_threshold = grid_spacing * rebalance_threshold_pct
                    
                    if abs(order['price'] - target['price']) <= rebalance_threshold:
                        orders_to_keep_ids.add(order['clientOrderId'])
                        match_found = True
                        break
            if not match_found:
                orders_to_create.append(target)
        
        # 所有未被保留的现有订单都应被取消
        for order in current_orders_copy:
            if order['clientOrderId'] not in orders_to_keep_ids:
                orders_to_cancel.append(order)

        # 执行操作
        if orders_to_cancel:
            cancel_tasks = [self.cancel_order(o['id'], o['clientOrderId']) for o in orders_to_cancel]
            await asyncio.gather(*cancel_tasks)
            logger.info(f"已取消 {len(orders_to_cancel)} 个过时订单。")

        if orders_to_create:
            create_tasks = [
                self.place_order(t['side'], t['positionSide'], t['quantity'], t['price'])
                for t in orders_to_create
            ]
            await asyncio.gather(*create_tasks)
            logger.info(f"已创建 {len(orders_to_create)} 个新网格订单。")

    async def check_circuit_breaker(self):
        """风险熔断检查"""
        if not self.atr_value or not self.grid_lower_bound: return

        # 在上升趋势中，我们只关心向下的熔断
        if self.current_trend == "UP":
            meltdown_price = self.grid_lower_bound - (self.atr_value * self.config['risk_management']['circuit_breaker_atr_multiplier'])
            if self.last_price < meltdown_price:
                logger.critical("="*20 + " 风险熔断触发！ " + "="*20)
                logger.critical(f"当前价格 {self.last_price:.2f} 已跌破熔断线 {meltdown_price:.2f}")
                logger.critical("将立即清空所有头寸和挂单，并暂停策略！")
                
                self.strategy_paused = True
                await self.cancel_all_open_orders()
                await self.close_all_positions()
                
                logger.critical("熔断操作执行完毕。策略已暂停，需要人工干预才能重启。")
    
    # ====================================================================================
    # --- 交易与状态管理 (继承并优化自V1.1) ---
    # ====================================================================================

    async def place_order(self, side, position_side, amount, price):
        if amount <= self.market['limits']['amount']['min']: return
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            # 平仓单数量不能超过持仓量
            if side == 'sell' and position_side == 'LONG':
                amount = min(amount, self.long_position_size)
                if amount <= 0: return # 没有持仓可平

            client_order_id = f"x-deltatrend-{int(time.time() * 1000)}"
            params = {'positionSide': position_side.upper(), 'newClientOrderId': client_order_id}
            
            new_order = await run_sync(self.exchange.create_order, self.symbol, 'limit', side, amount, price, params)
            if new_order:
                self.open_orders[new_order['clientOrderId']] = new_order
        except Exception as e:
            logger.error(f"下单失败: {side} {amount:.4f} @ {price:.4f}, 原因: {e}")

    async def cancel_order(self, order_id, client_order_id):
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            await run_sync(self.exchange.cancel_order, order_id, self.symbol)
        except Exception as e:
            if "Order does not exist" not in str(e):
                logger.error(f"撤单失败 (ID: {order_id}): {e}")
        finally:
            self.open_orders.pop(client_order_id, None)

    async def cancel_all_open_orders(self):
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            await run_sync(self.exchange.cancel_all_orders, self.symbol)
            self.open_orders.clear()
            logger.info("所有挂单已成功请求取消。")
        except Exception as e:
            logger.error(f"批量撤销所有订单失败: {e}")

    async def close_all_positions(self):
        """市价平掉所有头寸"""
        run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
        try:
            await self.full_state_sync() # 确保拿到最新的持仓
            if self.long_position_size > 0:
                logger.warning(f"正在市价平掉 {self.long_position_size} 的多头头寸...")
                await run_sync(self.exchange.create_market_sell_order, self.symbol, self.long_position_size, {'positionSide': 'LONG'})
            if self.short_position_size > 0:
                logger.warning(f"正在市价平掉 {self.short_position_size} 的空头头寸...")
                await run_sync(self.exchange.create_market_buy_order, self.symbol, self.short_position_size, {'positionSide': 'SHORT'})
        except Exception as e:
            logger.error(f"市价平仓失败: {e}")


    async def full_state_sync(self):
        async with self.lock:
            try:
                run_sync = lambda func, *args, **kwargs: asyncio.to_thread(func, *args, **kwargs)
                
                futures = [
                    run_sync(self.exchange.fetch_balance),
                    run_sync(self.exchange.fetch_positions, [self.symbol]),
                    run_sync(self.exchange.fetch_open_orders, self.symbol),
                    run_sync(self.exchange.fetch_ticker, self.symbol)
                ]
                balance, positions, open_orders_raw, ticker = await asyncio.gather(*futures)

                # 更新余额
                self.account_balance = float(balance.get(self.config['contract_type'], {}).get('total', 0))
                # 更新价格
                self.last_price = ticker['last']
                # 更新持仓
                long_pos = next((p for p in positions if p['info']['positionSide'] == 'LONG'), None)
                self.long_position_size = float(long_pos['contracts']) if long_pos else 0.0
                self.long_position_avg_price = float(long_pos['entryPrice']) if long_pos else 0.0
                short_pos = next((p for p in positions if p['info']['positionSide'] == 'SHORT'), None)
                self.short_position_size = float(short_pos['contracts']) if short_pos else 0.0
                self.total_position_contracts = self.long_position_size + self.short_position_size
                # 更新挂单
                self.open_orders = {o['clientOrderId']: o for o in open_orders_raw if 'clientOrderId' in o}
                
                self.last_state_sync_time = time.time()
                logger.debug(f"状态同步完成。余额:{self.account_balance:.2f}, 持仓:L={self.long_position_size:.4f},S={self.short_position_size:.4f}. 挂单:{len(self.open_orders)}")
            except Exception as e:
                logger.error(f"状态完全同步失败: {e}", exc_info=True)


    # --- V1.1 辅助函数 (基本保持不变) ---
    async def set_hedge_mode(self):
        try:
            await asyncio.to_thread(self.exchange.fapiPrivatePostPositionSideDual, {"dualSidePosition": "true"})
            logger.info("双向持仓模式已成功设置。")
        except Exception as e:
            if 'code=-4059' in str(e): logger.info("当前已是双向持仓模式。")
            else: logger.error(f"设置双向持仓模式失败: {e}")

    async def set_leverage(self):
        try:
            await asyncio.to_thread(self.exchange.set_leverage, self.config['leverage'], self.market['id'])
            logger.info(f"杠杆已设置为 {self.config['leverage']}x")
        except Exception as e:
            if 'leverage not modified' in str(e): logger.info(f"杠杆已是 {self.config['leverage']}x。")
            else: logger.error(f"设置杠杆失败: {e}")

    async def fetch_listen_key(self):
        try:
            response = await asyncio.to_thread(self.exchange.fapiPrivatePostListenKey)
            logger.info("成功获取新的 listen key。")
            return response['listenKey']
        except Exception as e:
            logger.error(f"获取 listen key 失败: {e}", exc_info=True)
            raise

    async def keep_listen_key_alive(self):
        while not self.is_shutting_down:
            await asyncio.sleep(1800)
            try:
                await asyncio.to_thread(self.exchange.fapiPrivatePutListenKey, {'listenKey': self.listen_key})
                logger.info("ListenKey 已成功延长。")
            except Exception as e:
                logger.error(f"延长 listen key 失败: {e}")
                self.listen_key = await self.fetch_listen_key()


# ====================================================================================
# --- 主程序入口 ---
# ====================================================================================
async def main():
    bot = None
    try:
        logger.info(f"启动 Delta趋势偏斜网格策略 V4.0 - [{CONFIG['coin_name']}]")
        bot = DeltaTrendGridBot(CONFIG)
        await bot.run()
    except KeyboardInterrupt:
        logger.info("检测到用户中断 (Ctrl+C)，开始优雅关闭...")
    except Exception as e:
        logger.critical(f"程序顶层发生未捕获的严重错误: {e}", exc_info=True)
    finally:
        if bot:
            await bot.close()
    logger.info("程序已完全退出。")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())