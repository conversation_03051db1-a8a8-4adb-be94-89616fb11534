#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接测试脚本 - 用于验证网络连接和API配置
"""

import asyncio
import aiohttp
import ccxt.async_support as ccxt_async
import logging
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger()

# 从主程序复制配置
CONFIG = {
    "api_key": "USlBuHx9zNwxXFtecEQZDOXXxbHhKMIViMeRaELIyjOHGCcdkoESgmxpu4bWi47c",
    "api_secret": "lxNiaI54wlWAyxpZ5oNajJ84MoQYodnakDWfoQiuONNTtU9YKsKS14tRwgBi7eUt",
    "use_proxy": True,
    "proxy_url": "http://127.0.0.1:7897",
    "coin_name": "SOL",
    "contract_type": "USDT",
}

async def test_proxy():
    """测试代理连接"""
    if not CONFIG.get('use_proxy'):
        logger.info("未配置代理，跳过代理测试")
        return True
    
    proxy = CONFIG['proxy_url']
    logger.info(f"测试代理连接: {proxy}")
    
    test_urls = [
        "http://httpbin.org/ip",
        "http://www.google.com",
        "https://www.baidu.com"
    ]
    
    timeout = aiohttp.ClientTimeout(total=10)
    
    for url in test_urls:
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, proxy=proxy) as response:
                    if response.status == 200:
                        logger.info(f"代理测试成功: {url} -> 状态码: {response.status}")
                        return True
                    else:
                        logger.warning(f"代理测试异常: {url} -> 状态码: {response.status}")
        except Exception as e:
            logger.warning(f"代理测试失败: {url} -> {e}")
    
    logger.error("所有代理测试都失败了")
    return False

async def test_binance_api():
    """测试币安API连接"""
    logger.info("测试币安API连接...")
    
    # 基础HTTP测试
    proxy = CONFIG['proxy_url'] if CONFIG.get('use_proxy') else None
    timeout = aiohttp.ClientTimeout(total=30)
    
    test_endpoints = [
        "https://fapi.binance.com/fapi/v1/ping",
        "https://fapi.binance.com/fapi/v1/time",
        "https://fapi.binance.com/fapi/v1/exchangeInfo"
    ]
    
    logger.info("1. 基础HTTP测试...")
    for endpoint in test_endpoints:
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                start_time = time.time()
                async with session.get(endpoint, proxy=proxy) as response:
                    elapsed = time.time() - start_time
                    if response.status == 200:
                        logger.info(f"✓ {endpoint} -> 状态码: {response.status}, 耗时: {elapsed:.2f}s")
                    else:
                        logger.warning(f"✗ {endpoint} -> 状态码: {response.status}, 耗时: {elapsed:.2f}s")
        except Exception as e:
            logger.error(f"✗ {endpoint} -> 错误: {e}")
    
    # CCXT测试
    logger.info("2. CCXT库测试...")
    try:
        proxies = {'http': proxy, 'https': proxy} if proxy else None
        
        exchange = ccxt_async.binanceusdm({
            'apiKey': CONFIG['api_key'],
            'secret': CONFIG['api_secret'],
            'timeout': 30000,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
                'recvWindow': 10000,
            },
            'proxies': proxies
        })
        
        # 测试ping
        try:
            ping_result = await exchange.fapiPublicGetPing()
            logger.info(f"✓ CCXT Ping成功: {ping_result}")
        except Exception as e:
            logger.error(f"✗ CCXT Ping失败: {e}")
        
        # 测试获取服务器时间
        try:
            server_time = await exchange.fetch_time()
            logger.info(f"✓ 服务器时间获取成功: {server_time}")
        except Exception as e:
            logger.error(f"✗ 服务器时间获取失败: {e}")
        
        # 测试市场数据
        try:
            await exchange.load_markets()
            symbol = f"{CONFIG['coin_name']}/{CONFIG['contract_type']}:{CONFIG['contract_type']}"
            if symbol in exchange.markets:
                logger.info(f"✓ 市场数据加载成功，找到交易对: {symbol}")
            else:
                logger.error(f"✗ 交易对不存在: {symbol}")
        except Exception as e:
            logger.error(f"✗ 市场数据加载失败: {e}")
        
        # 测试账户信息（需要API密钥）
        try:
            balance = await exchange.fetch_balance()
            usdt_balance = balance.get('USDT', {}).get('total', 'N/A')
            logger.info(f"✓ 账户余额获取成功，USDT余额: {usdt_balance}")
        except Exception as e:
            logger.error(f"✗ 账户余额获取失败: {e}")
            logger.error("这可能是API密钥权限问题")
        
        await exchange.close()
        
    except Exception as e:
        logger.error(f"CCXT初始化失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    logger.info("开始连接测试...")
    logger.info("=" * 50)
    
    # 测试代理
    proxy_ok = await test_proxy()
    logger.info("=" * 50)
    
    # 测试API
    api_ok = await test_binance_api()
    logger.info("=" * 50)
    
    # 总结
    logger.info("测试结果总结:")
    logger.info(f"代理连接: {'✓ 正常' if proxy_ok else '✗ 异常'}")
    logger.info(f"API连接: {'✓ 正常' if api_ok else '✗ 异常'}")
    
    if proxy_ok and api_ok:
        logger.info("🎉 所有测试通过！程序应该可以正常运行。")
    else:
        logger.warning("⚠️  部分测试失败，程序可能无法正常运行。")
        if not proxy_ok:
            logger.warning("请检查代理服务器设置")
        if not api_ok:
            logger.warning("请检查API密钥和网络连接")

if __name__ == "__main__":
    import sys
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
